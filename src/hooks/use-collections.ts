'use client';

import {
	addTermToCollection<PERSON>pi,
	addWordsToCollectionApi,
	bulkDeleteWordsFromCollectionApi,
	createCollectionApi,
	deleteCollectionApi,
	getCollectionApi,
	getCollectionsApi,
	getWordsByCollectionApi,
	getWordsByIdsApi,
	getWordsToReviewApi,
	removeWordsFromCollectionApi,
	searchWordsApi,
	updateCollectionApi,
} from '@/backend/api';
import { CollectionsContext, CollectionsContextType, useLoadingError } from '@/contexts';
import { getAllCollections, saveCollection, saveCollections } from '@/lib/indexed-db';
import { CollectionWithDetail, WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { useCallback, useContext, useEffect } from 'react';

export function useCollectionsContext(): CollectionsContextType {
	const context = useContext(CollectionsContext);
	if (context === undefined)
		throw new Error('useCollectionsContext must be used within a CollectionsProvider');
	return context;
}

export function useCollections() {
	const context = useCollectionsContext();
	const {
		collections,
		currentCollection,
		setError,
		setCollections,
		setCurrentCollectionWords,
		setCurrentCollection,
	} = useCollectionsContext();
	const loadingErrorHelper = useLoadingError('collections');

	const fetchCollections = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'fetch');
		start();
		try {
			const collectionsData = await getCollectionsApi();
			// Save to cache after getting data from API
			setCollections(collectionsData);
			await saveCollections(collectionsData);
			end();
		} catch (apiError) {
			end(apiError instanceof Error ? apiError : new Error('Failed to fetch collections'));
		}
	}, [loadingErrorHelper]);

	const getCollection = useCallback(
		async (id: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'get');
			start();
			try {
				const collectionData = await getCollectionApi(id);
				await saveCollection(collectionData);
				end();
				return collectionData;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to fetch collection'));
				return null;
			}
		},
		[loadingErrorHelper]
	);

	const getCollectionWordsToReview = useCallback(
		async (id: string, limit?: number) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'getWordsToReview');
			start();
			try {
				const wordsFromApi = await getWordsToReviewApi(id, limit);
				end();
				return wordsFromApi;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to get review words'));
				return [];
			}
		},
		[loadingErrorHelper]
	);

	const searchCollections = useCallback(
		async (term: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'search');
			start();
			try {
				// Search in current collections first
				if (collections.length > 0) {
					const filtered = collections.filter((collection) =>
						collection.name.toLowerCase().includes(term.toLowerCase())
					);
					end();
					return filtered;
				}

				// If no collections loaded, fetch and search
				const result = await getCollectionsApi();
				const filtered = (result as CollectionWithDetail[]).filter((collection) =>
					collection.name.toLowerCase().includes(term.toLowerCase())
				);
				end();
				return filtered;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to search'));
				return [];
			}
		},
		[loadingErrorHelper, collections]
	);

	const createCollection = useCallback(
		async (name: string, target_language: Language, source_language: Language) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'create');
			start();
			try {
				const newCollection = await createCollectionApi(
					name,
					target_language,
					source_language
				);

				if (newCollection) {
					await saveCollection(newCollection); // Save to IndexedDB
					setCollections((prevCollections) => [...prevCollections, newCollection]); // Add to state
					end();
					return newCollection;
				} else {
					throw new Error('Create operation did not return the new collection.');
				}
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to create collection'));
				return null;
			}
		},
		[loadingErrorHelper]
	);

	const updateCollection = useCallback(
		async (id: string, data: { name?: string }) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'update');
			start();
			try {
				const updatedCollection = await updateCollectionApi(id, data);

				if (updatedCollection) {
					await saveCollection(updatedCollection);
					setCollections((prevCollections) =>
						prevCollections.map((c) =>
							c.id === id ? { ...c, ...updatedCollection } : c
						)
					);
					if (currentCollection?.id === id) setCurrentCollection(updatedCollection);
					end();
					return updatedCollection;
				} else {
					throw new Error('Update operation did not return the updated collection.');
				}
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to update collection'));
				return null;
			}
		},
		[loadingErrorHelper, currentCollection]
	);

	const deleteCollection = useCallback(
		async (id: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'delete');
			start();
			try {
				await deleteCollectionApi(id);
				setCollections((prev) => prev.filter((c) => c.id !== id));
				if (currentCollection?.id === id) setCurrentCollection(null);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to delete'));
			}
		},
		[loadingErrorHelper, currentCollection]
	);

	const refreshCollection = useCallback(
		async (id: string): Promise<CollectionWithDetail | null> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'get');
			start();
			try {
				const result = await getCollectionApi(id);
				const collectionData = result as CollectionWithDetail;
				await saveCollection(collectionData);
				setCollections((prev) => prev.map((c) => (c.id === id ? collectionData : c)));
				if (currentCollection?.id === id) setCurrentCollection(collectionData);
				end();
				return collectionData;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to refresh collection'));
				return null;
			}
		},
		[loadingErrorHelper, currentCollection]
	);

	const addTermToCollection = useCallback(
		async (collectionId: string, term: string, language: Language) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'addTerm');
			start();
			try {
				const result = await addTermToCollectionApi(collectionId, term, language);
				// Update local state and cache
				await saveCollection(result);
				setCollections((prev) => prev.map((c) => (c.id === collectionId ? result : c)));
				// Update current collection if it's the same
				if (currentCollection?.id === collectionId) {
					setCurrentCollection(result);
				}
				end();
				return result;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to add term'));
				return null;
			}
		},
		[loadingErrorHelper, currentCollection]
	);

	const addWordsToCollection = useCallback(
		async (collectionId: string, wordIds: string[]) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'addWords');
			start();
			try {
				const result = await addWordsToCollectionApi(collectionId, wordIds);
				// Update local state and cache
				await saveCollection(result);
				setCollections((prev) => prev.map((c) => (c.id === collectionId ? result : c)));
				// Update current collection if it's the same
				if (currentCollection?.id === collectionId) {
					setCurrentCollection(result);
				}
				end();
				return result;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to add words'));
				return null;
			}
		},
		[loadingErrorHelper, currentCollection]
	);

	const removeWordsFromCollection = useCallback(
		async (collectionId: string, wordIds: string[]) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'removeWords');
			start();
			try {
				const result = await removeWordsFromCollectionApi(collectionId, wordIds);
				// Update local state and cache
				await saveCollection(result);
				setCollections((prev) => prev.map((c) => (c.id === collectionId ? result : c)));
				// Update current collection if it's the same
				if (currentCollection?.id === collectionId) setCurrentCollection(result);
				end();
				return result;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to remove words'));
				return null;
			}
		},
		[loadingErrorHelper, currentCollection]
	);

	const searchWords = useCallback(
		async (searchTerm: string) => {
			if (!currentCollection?.id) {
				setError(new Error('No current collection selected'));
				return;
			}
			const { start, end } = loadingErrorHelper(() => {}, setError, 'wordsSearch');
			start();
			try {
				const result = await searchWordsApi(currentCollection.id, searchTerm);
				setCurrentCollectionWords(result);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to search words'));
			}
		},
		[currentCollection?.id, loadingErrorHelper]
	);

	const fetchCurrentCollectionWords = useCallback(async () => {
		if (!currentCollection?.id) {
			setCurrentCollectionWords([]);
			return;
		}
		const { start, end } = loadingErrorHelper(() => {}, setError, 'fetchWords');
		start();
		try {
			const result = await getWordsByCollectionApi(currentCollection.id);
			setCurrentCollectionWords(result as WordDetail[]);
			end();
		} catch (err) {
			end(
				err instanceof Error
					? err
					: new Error('Failed to fetch words for current collection')
			);
		}
	}, [currentCollection?.id, loadingErrorHelper]);

	const getCurrentCollectionWordsToReview = useCallback(
		async (limit?: number) => {
			if (!currentCollection?.id) {
				setError(new Error('No current collection selected'));
				return;
			}
			const { start, end } = loadingErrorHelper(() => {}, setError, 'getWordsToReviewWords');
			start();
			try {
				const result = await getWordsToReviewApi(currentCollection.id, limit);
				setCurrentCollectionWords(result as WordDetail[]);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to fetch words to review'));
			}
		},
		[currentCollection?.id, loadingErrorHelper]
	);

	const refreshCurrentCollection = useCallback(async (): Promise<CollectionWithDetail | null> => {
		if (!currentCollection?.id) {
			return null;
		}
		return await refreshCollection(currentCollection.id);
	}, [currentCollection, refreshCollection]);

	const bulkDeleteWordsFromCurrentCollection = useCallback(
		async (wordIds: string[]) => {
			if (!currentCollection?.id) {
				setError(new Error('No current collection selected'));
				return;
			}
			const { start, end } = loadingErrorHelper(() => {}, setError, 'bulkDeleteWords');
			start();
			try {
				await bulkDeleteWordsFromCollectionApi(currentCollection.id, wordIds);
				await fetchCurrentCollectionWords();
				await refreshCurrentCollection();
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to delete words'));
			}
		},
		[
			currentCollection?.id,
			fetchCurrentCollectionWords,
			refreshCurrentCollection,
			loadingErrorHelper,
		]
	);

	const fetchWordsByCollection = useCallback(
		async (collectionId: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'fetchWords');
			start();
			try {
				const result = await getWordsByCollectionApi(collectionId);
				if (collectionId === currentCollection?.id)
					setCurrentCollectionWords(result as WordDetail[]);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to fetch words by collection'));
			}
		},
		[loadingErrorHelper]
	);

	const getWordsToReviewWords = useCallback(
		async (collectionId: string, limit?: number) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'getWordsToReviewWords');
			start();
			try {
				const result = await getWordsToReviewApi(collectionId, limit);
				if (collectionId === currentCollection?.id)
					setCurrentCollectionWords(result as WordDetail[]);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to fetch words to review'));
			}
		},
		[loadingErrorHelper]
	);

	const bulkDeleteWords = useCallback(
		async (collectionId: string, wordIds: string[]) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'bulkDeleteWords');
			start();
			try {
				await bulkDeleteWordsFromCollectionApi(collectionId, wordIds);
				await fetchWordsByCollection(collectionId);
				end();
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to delete words'));
			}
		},
		[fetchWordsByCollection, loadingErrorHelper]
	);

	const getWordById = useCallback(async (id: string): Promise<WordDetail | null> => {
		try {
			const result = await getWordsByIdsApi([id]);
			return (result as WordDetail[])[0] || null;
		} catch (err) {
			setError(err instanceof Error ? err : new Error('Failed to fetch word'));
			return null;
		}
	}, []);

	const fetchWord = useCallback(
		async (id: string): Promise<WordDetail | null> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'fetchWord');
			start();
			try {
				const result = await getWordsByIdsApi([id]);
				const word = (result as WordDetail[])[0] || null;
				end();
				return word;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to fetch word'));
				return null;
			}
		},
		[loadingErrorHelper]
	);

	const setCurrentCollectionById = useCallback(
		async (id: string): Promise<CollectionWithDetail | null> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'setCurrent');
			start();
			try {
				const existingCollection = collections.find((c) => c.id === id);
				if (existingCollection) {
					setCurrentCollection(existingCollection);
					setCurrentCollectionWords([]);
					end();
					return existingCollection;
				}

				const collection = await getCollection(id);
				if (collection) {
					setCurrentCollection(collection);
					setCurrentCollectionWords([]);
				}
				end();
				return collection;
			} catch (err) {
				end(err instanceof Error ? err : new Error('Failed to set current collection'));
				return null;
			}
		},
		[collections, getCollection, loadingErrorHelper]
	);

	const clearCurrentCollection = useCallback(() => {
		setCurrentCollection(null);
		setCurrentCollectionWords([]);
	}, []);

	const addTermToCurrentCollection = useCallback(
		async (term: string, language: Language): Promise<CollectionWithDetail | null> => {
			if (!currentCollection?.id) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await addTermToCollection(currentCollection.id, term, language);
		},
		[currentCollection, addTermToCollection]
	);

	const addWordsToCurrentCollection = useCallback(
		async (wordIds: string[]): Promise<CollectionWithDetail | null> => {
			if (!currentCollection?.id) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await addWordsToCollection(currentCollection.id, wordIds);
		},
		[currentCollection, addWordsToCollection]
	);

	const removeWordsFromCurrentCollection = useCallback(
		async (wordIds: string[]): Promise<CollectionWithDetail | null> => {
			if (!currentCollection?.id) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await removeWordsFromCollection(currentCollection.id, wordIds);
		},
		[currentCollection, removeWordsFromCollection]
	);

	const updateCurrentCollection = useCallback(
		async (data: { name?: string }): Promise<CollectionWithDetail | null> => {
			if (!currentCollection?.id) {
				setError(new Error('No current collection selected'));
				return null;
			}
			return await updateCollection(currentCollection.id, data);
		},
		[currentCollection, updateCollection]
	);

	const deleteCurrentCollection = useCallback(async (): Promise<void> => {
		if (!currentCollection?.id) {
			setError(new Error('No current collection selected'));
			return;
		}
		await deleteCollection(currentCollection.id);
	}, [currentCollection, deleteCollection]);

	useEffect(() => {
		const loadInitialCollections = async () => {
			try {
				const cachedCollections = await getAllCollections();
				if (cachedCollections && cachedCollections.length > 0) {
					setCollections(cachedCollections);
				}
			} catch (cacheError) {
				console.warn('Failed to load collections from cache:', cacheError);
			}
			fetchCollections();
		};
		loadInitialCollections();
	}, [fetchCollections]);

	return {
		context,

		// collections
		searchCollections,
		createCollection,
		fetchCollections,

		// current collection
		setCurrentCollection,
		setCurrentCollectionById,
		refreshCurrentCollection,
		clearCurrentCollection,
		updateCurrentCollection,
		deleteCurrentCollection,

		// words
		fetchWord,
		searchWords,
		getWordById,
		bulkDeleteWords,
		addTermToCollection,
		addWordsToCollection,
		removeWordsFromCollection,
		fetchCurrentCollectionWords,
		fetchWordsByCollection,
		getWordsToReviewWords,
		getCurrentCollectionWordsToReview,
		bulkDeleteWordsFromCurrentCollection,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		removeWordsFromCurrentCollection,
		getCollectionWordsToReview,
	};
}
