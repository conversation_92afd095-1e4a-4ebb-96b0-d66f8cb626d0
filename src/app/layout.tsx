import '@/app/globals.css';
import { ClientSettings } from '@/app/components/client-settings';
import { ProgressBar, ThemeProvider, Toaster } from '@/components/ui';
import type { Metadata } from 'next';
import { <PERSON><PERSON>st, <PERSON>eist_Mono } from 'next/font/google';
import { TranslationProvider } from '@/contexts/translation-context';
// import { LoadingSpinner } from '@/contexts/loading-context';
import { cookies } from 'next/headers';
import { providerLoginApi } from '@/backend/api';
import { config } from '@/config';
import { Provider } from '@prisma/client';

const geist = Geist({
	subsets: ['latin'],
	variable: '--font-geist',
	preload: true,
});

const geistMono = Geist_Mono({
	subsets: ['latin'],
	variable: '--font-geist-mono',
	preload: true,
});

export const metadata: Metadata = {
	title: 'Vocab',
	description: 'Learn new vocabulary with AI assistance',
};

export default async function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	// Server-side authentication logic
	const cookieStore = await cookies();
	const existingToken = cookieStore.get(config.auth.jwtCookieName)?.value;

	// If no token exists, perform authentication
	if (!existingToken) {
		try {
			let user: { provider: Provider; provider_id: string };

			if (process.env.NODE_ENV === 'production') {
				// In production, we cannot access Telegram Web Apps data on server-side
				// The authentication will be handled by client-side components or middleware
				// For now, we'll let the app continue without auto-authentication
				console.log(
					'Production mode: Telegram authentication should be handled by client-side or middleware'
				);
			} else {
				// Use default user data in development
				const defaultUser = config.auth.defaultUser;
				user = {
					provider: defaultUser.provider,
					provider_id: defaultUser.provider_id,
				};

				// Perform server-side login for development
				await providerLoginApi(user.provider, user.provider_id);
				console.log('Development mode: Auto-authenticated with default user');
			}
		} catch (error) {
			console.error('Server-side authentication failed:', error);
			// Continue rendering, authentication can be handled elsewhere
		}
	}

	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<meta
					name="viewport"
					content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
				/>
				<link rel="manifest" href="/manifest.json" />
			</head>
			<body
				className={`${geist.variable} ${geistMono.variable} min-h-screen flex flex-col`}
				role="application"
				aria-label="Vocab Learning Application"
				aria-live="polite"
				suppressHydrationWarning
			>
				<ThemeProvider>
					<TranslationProvider>
						<main className="flex-grow px-3 sm:px-9 py-3 sm:py-8">
							<div className="container mx-auto">
								<div className="mb-6 flex flex-col items-center justify-center sm:p-8 md:p-24 max-w-4xl mx-auto">
									{children}
								</div>
							</div>
						</main>
						{/* <LoadingSpinner /> */}
						<ProgressBar />
						<Toaster />
						<ClientSettings />
					</TranslationProvider>
				</ThemeProvider>
			</body>
		</html>
	);
}
