'use client';

import { But<PERSON>, Translate } from '@/components/ui';
import { useCollections } from '@/contexts';
import { useTranslation } from '@/contexts';
import { ArrowLef<PERSON>, Pencil } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ReactNode, useCallback, useEffect, useState, useMemo } from 'react';
import { RenameCollectionDialog } from './rename-collection-dialog';
import { useLoading } from '@/contexts/loading-context';
import { CollectionWithDetail } from '@/models';

export function CollectionDetailLayoutClient({
	children,
	initialCollection,
}: {
	children: ReactNode;
	initialCollection: CollectionWithDetail;
}) {
	// const router = useRouter();
	// const { setLoading: setGlobalLoading } = useLoading();
	// const { t } = useTranslation();
	const {
		currentCollection,
		// updateCurrentCollection,
		// setCurrentCollection,
		// loading,
		// error,
		// clearError,
	} = useCollections();

	// const shouldUpdateCollection = useMemo(() => {
	// 	if (!initialCollection) return false;
	// 	if (!currentCollection) return true;
	// 	if (currentCollection.id !== initialCollection.id) return true;
	// 	// Check if the collection data has actually changed
	// 	return JSON.stringify(currentCollection) !== JSON.stringify(initialCollection);
	// }, [initialCollection, currentCollection]);

	// useEffect(() => {
	// 	if (shouldUpdateCollection) {
	// 		setCurrentCollection(initialCollection);
	// 	}
	// }, [shouldUpdateCollection, initialCollection, setCurrentCollection]);

	// const [isEditingName, setIsEditingName] = useState(false);

	// // Clear errors when component unmounts or collection changes
	// useEffect(() => {
	// 	if (error) clearError();
	// }, [error, clearError]);

	// const renameCollection = useCallback(
	// 	async ({ name }: { name: string }) => {
	// 		try {
	// 			await updateCurrentCollection({ name });
	// 		} catch (err) {
	// 			throw err;
	// 		}
	// 	},
	// 	[updateCurrentCollection]
	// );

	// const handleStartRename = useCallback(() => {
	// 	setIsEditingName(true);
	// }, []);

	// const handleBack = useCallback(() => {
	// 	if (currentCollection) {
	// 		const path = window.location.pathname;
	// 		const expectedPath = `/collections/${currentCollection.id}`;
	// 		if (path === expectedPath) {
	// 			router.push('/collections');
	// 			return;
	// 		}
	// 		router.push('/collections/' + currentCollection.id);
	// 	} else {
	// 		router.push('/collections');
	// 	}
	// }, [router, currentCollection]);

	return (
		currentCollection && (
			<div className="w-full sm:py-8">
				{/* <div className="flex items-center justify-between mb-6">
					<div className="flex items-center gap-2">
						<Button
							variant="ghost"
							size="sm"
							aria-label={t('ui.back_to_collections_aria')}
							onClick={handleBack}
						>
							<ArrowLeft className="h-4 w-4" />
							<span className="hidden sm:inline">
								<Translate text="ui.back" />
							</span>
						</Button>
						<h1 className="text-2xl font-bold">{currentCollection.name}</h1>
						<button
							type="button"
							className="ml-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
							onClick={handleStartRename}
							disabled={loading.update}
							aria-label={t('collections.rename_collection_aria')}
						>
							<Pencil className="h-4 w-4 hover:text-gray-700 dark:hover:text-gray-300 transition-colors" />
						</button>
					</div>
				</div> */}

				{/* {error && (
					<div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
						<p className="text-sm text-red-600 dark:text-red-400">{error.message}</p>
						<button
							onClick={clearError}
							className="mt-2 text-xs text-red-600 dark:text-red-400 underline hover:no-underline"
						>
							<Translate text="ui.dismiss" />
						</button>
					</div>
				)} */}

				{/* <RenameCollectionDialog
					open={isEditingName}
					onOpenChange={setIsEditingName}
					collection={currentCollection}
					renameCollection={renameCollection}
					renameLoading={loading.update}
					renameError={error}
				/> */}

				{/* <div className="mt-4">{children}</div> */}
			</div>
		)
	);
}
