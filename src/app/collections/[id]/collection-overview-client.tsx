'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, Translate } from '@/components/ui';
import { useCollections } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/lib';
import { useEffect } from 'react';
import { CollectionWithDetail } from '@/models';
import {
	BookOpen,
	Calendar,
	CheckCircle2,
	Clock,
	Edit3,
	FileText,
	Globe,
	GraduationCap,
	Hash,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Target,
	Zap,
} from 'lucide-react';
import Link from 'next/link';

export function CollectionOverviewClient({
	initialCollection,
}: {
	initialCollection?: CollectionWithDetail;
}) {
	const { currentCollection, setCurrentCollection, loading, error, clearError } =
		useCollections();

	useEffect(() => {
		if (initialCollection) setCurrentCollection(initialCollection);
	}, [initialCollection, setCurrentCollection]);

	// Clear errors when component unmounts or collection changes
	useEffect(() => {
		if (error) {
			console.error(error);
			clearError();
		}
	}, [error, clearError]);

	const features = currentCollection
		? [
				{
					titleKey: 'collections.tabs.vocabulary',
					descriptionKey: 'collections.overview.vocabulary_desc',
					icon: GraduationCap,
					link: `/collections/${currentCollection.id}/vocabulary`, // Base path for vocabulary section
					subFeatures: [
						{
							titleKey: 'collections.tabs.generate_words',
							icon: Zap,
							subLink: '/generate', // Updated
						},
						{
							titleKey: 'collections.tabs.my_words_list',
							icon: ListChecks,
							subLink: '/my-words', // Updated
						},
						{
							titleKey: 'collections.tabs.review',
							icon: RefreshCw,
							subLink: '/review', // Updated
						},
						{
							titleKey: 'collections.tabs.multiple_choice_practice',
							icon: Target,
							subLink: '/mcq', // Updated
						},
					],
				},
				{
					titleKey: 'collections.tabs.paragraphs',
					descriptionKey: 'collections.overview.paragraphs_desc',
					icon: FileText,
					link: `/collections/${currentCollection.id}/paragraph`, // Assuming this is the base for paragraph features
					subFeatures: [
						{
							titleKey: 'collections.tabs.paragraph_practice',
							icon: Edit3,
							subLink: '/paragraph-practice', // Check if this needs update based on paragraph routes
						},
						{
							titleKey: 'qa_practice.tab_title',
							icon: MessageSquare,
							subLink: '/qa-practice', // Check if this needs update based on paragraph routes
						},
						{
							titleKey: 'collections.tabs.grammar_practice',
							icon: CheckCircle2,
							subLink: '/grammar-practice',
						},
					],
				},
		  ]
		: [];

	return (
		currentCollection && (
			<>
				{error && (
					<div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
						<p className="text-sm text-red-600 dark:text-red-400">{error.message}</p>
						<button
							onClick={clearError}
							className="mt-2 text-xs text-red-600 dark:text-red-400 underline hover:no-underline"
						>
							<Translate text="ui.dismiss" />
						</button>
					</div>
				)}
				<div className="container mx-auto py-6">
					<Card className="mb-8 bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 border-primary/20">
						<CardHeader>
							<CardTitle className="text-2xl font-semibold flex items-center gap-3">
								<BookOpen className="h-7 w-7 text-primary" />
								<Translate
									text="collections.overview.welcome_title"
									values={{ name: currentCollection.name }}
								/>
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
								<div className="flex items-center gap-2">
									<Globe className="h-4 w-4 text-blue-500" />
									<strong>
										<Translate text="languages.source_language" />:
									</strong>{' '}
									<Translate
										text={getTranslationKeyOfLanguage(
											currentCollection.source_language
										)}
									/>
								</div>
								<div className="flex items-center gap-2">
									<Globe className="h-4 w-4 text-green-500" />
									<strong>
										<Translate text="languages.target_language" />:
									</strong>{' '}
									<Translate
										text={getTranslationKeyOfLanguage(
											currentCollection.target_language
										)}
									/>
								</div>
								<div className="flex items-center gap-2">
									<Hash className="h-4 w-4 text-purple-500" />
									<strong>
										<Translate text="collections.overview.word_count" />:
									</strong>{' '}
									{currentCollection.word_ids.length}
								</div>
								<div className="flex items-center gap-2">
									<Calendar className="h-4 w-4 text-orange-500" />
									<strong>
										<Translate text="collections.overview.created_at" />:
									</strong>{' '}
									{new Date(currentCollection.created_at).toLocaleDateString()}
								</div>
								<div className="flex items-center gap-2">
									<Clock className="h-4 w-4 text-teal-500" />
									<strong>
										<Translate text="collections.overview.updated_at" />:
									</strong>{' '}
									{new Date(currentCollection.updated_at).toLocaleDateString()}
								</div>
							</div>
						</CardContent>
					</Card>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						{features.map((feature) => (
							<Card
								key={feature.titleKey}
								className="hover:shadow-lg transition-shadow duration-200 flex flex-col"
							>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-xl font-medium">
										<Translate text={feature.titleKey} />
									</CardTitle>
									<feature.icon className="h-6 w-6 text-muted-foreground" />
								</CardHeader>
								<CardContent className="flex-grow">
									<p className="text-sm text-muted-foreground mb-4">
										<Translate text={feature.descriptionKey} />
									</p>
									<div className="space-y-2 mb-4">
										{feature.subFeatures?.map((sub) => (
											<Link
												key={sub.titleKey}
												href={`${feature.link}${sub.subLink}`}
												passHref
											>
												<Button
													variant="ghost"
													size="sm"
													className="w-full justify-start text-left"
												>
													<sub.icon className="mr-2 h-4 w-4" />
													<Translate text={sub.titleKey} />
												</Button>
											</Link>
										))}
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</>
		)
	);
}
