'use client';

import { TranslationEvaluationResult } from '@/backend/services';
import {
	Button,
	DifficultySelector,
	Label,
	Textarea,
	Translate,
	WordGenerationForm,
} from '@/components/ui';
import { useCollections, useLLM } from '@/contexts';
import { useTranslation } from '@/contexts';
import { Difficulty, Keyword, Language } from '@prisma/client';
import React, { useCallback, useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { CollectionWithDetail } from '@/models';
import { useSharedPracticeLogic } from '../../use-shared-practice-logic';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';

// Constants
const PARAGRAPH_COUNT_FOR_TRANSLATION = 5;

// Types
interface ParagraphPracticeItem {
	originalText: string;
	userTranslation: string;
	evaluationResult?: TranslationEvaluationResult | null;
	isEvaluating: boolean;
}

// Components
const FeedbackSection = ({
	feedback,
	language,
}: {
	feedback: string;
	language: Language;
	collection: any;
	input?: string;
}) => (
	<div>
		<strong className="text-base">
			<Translate text={getTranslationKeyOfLanguage(language)} />:
		</strong>
		<p className="whitespace-pre-wrap text-sm">{feedback}</p>
	</div>
);

const SuggestionsList = ({
	suggestions,
	language,
}: {
	suggestions: string[];
	language: Language;
	collection: any;
	input?: string;
}) => (
	<div className="mt-1">
		<em className="text-sm">
			<Translate text={getTranslationKeyOfLanguage(language)} />:
		</em>
		<ul className="list-disc list-inside ml-4 text-sm">
			{suggestions.map((suggestion, idx) => (
				<li key={`${language}-${idx}`}>{suggestion}</li>
			))}
		</ul>
	</div>
);

const EvaluationResult = ({
	item,
	collection,
}: {
	item: ParagraphPracticeItem;
	collection: any;
	input?: string;
}) => {
	if (item.evaluationResult === null) {
		return (
			<div className="mt-2 p-2 bg-red-100 border-l-4 border-red-500 rounded text-sm text-red-700 dark:bg-red-900/50 dark:border-red-700 dark:text-red-200">
				<Translate text="paragraphs.evaluation_failed_title" />.
			</div>
		);
	}

	if (!item.evaluationResult) return null;

	const getScoreColor = (score: number | null | undefined): 'red' | 'green' | 'yellow' => {
		if (score === undefined || score === null) return 'red';
		return score >= 7 ? 'green' : 'yellow';
	};

	const color = getScoreColor(item.evaluationResult.score);
	const colorClasses = {
		green: 'border-green-500 bg-green-50 dark:border-green-700 dark:bg-green-900/30 text-green-800 dark:text-green-200',
		yellow: 'border-yellow-500 bg-yellow-50 dark:border-yellow-700 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200',
		red: 'border-red-500 bg-red-50 dark:border-red-700 dark:bg-red-900/30 text-red-800 dark:text-red-200',
	};

	return (
		<div className={`mt-2 p-3 rounded border-l-4 ${colorClasses[color]}`}>
			<div className="flex items-center gap-2">
				<span className="font-semibold">
					<Translate text="paragraphs.feedback_title" />
				</span>
				{item.evaluationResult.score != null && (
					<span className="ml-2 text-sm font-medium">
						<strong>
							<Translate text="paragraphs.score_label" />:
						</strong>{' '}
						{item.evaluationResult.score}/10
					</span>
				)}
			</div>

			<div className="mt-1 space-y-2">
				{item.evaluationResult.feedback?.source_language && (
					<FeedbackSection
						feedback={item.evaluationResult.feedback.source_language}
						language={collection.source_language}
						collection={collection}
					/>
				)}
				{item.evaluationResult.feedback?.target_language && (
					<FeedbackSection
						feedback={item.evaluationResult.feedback.target_language}
						language={collection.target_language}
						collection={collection}
					/>
				)}
			</div>

			{item.evaluationResult.suggestions &&
				(item.evaluationResult.suggestions.source_language?.length > 0 ||
					item.evaluationResult.suggestions.target_language?.length > 0) && (
					<div className="mt-2 pt-2 border-t border-gray-300 dark:border-zinc-700">
						<strong className="text-base">
							<Translate text="paragraphs.suggestions_label" />:
						</strong>
						{item.evaluationResult.suggestions.source_language?.length > 0 && (
							<SuggestionsList
								suggestions={item.evaluationResult.suggestions.source_language}
								language={collection.source_language}
								collection={collection}
							/>
						)}
						{item.evaluationResult.suggestions.target_language?.length > 0 && (
							<SuggestionsList
								suggestions={item.evaluationResult.suggestions.target_language}
								language={collection.target_language}
								collection={collection}
							/>
						)}
					</div>
				)}
		</div>
	);
};

const PracticeItemCard = ({
	item,
	index,
	totalItems,
	collection,
	t,
	onTextAreaChange,
	onEvaluate,
}: {
	item: ParagraphPracticeItem;
	index: number;
	totalItems: number;
	collection: any;
	t: (key: string) => string;
	onTextAreaChange: (index: number, e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	onEvaluate: (index: number) => void;
}) => (
	<div className="p-4 border rounded-lg shadow-lg transition-all bg-card dark:bg-zinc-900 dark:border-zinc-800 space-y-3 relative">
		<div className="flex items-center justify-between mb-2">
			<span className="text-sm font-semibold text-card-foreground dark:text-gray-200">
				<Translate text="paragraphs.original_text_label" /> ({index + 1}/{totalItems})
				<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
					<Translate text={getTranslationKeyOfLanguage(collection.source_language)} />
				</span>
			</span>
		</div>

		<p className="whitespace-pre-wrap bg-muted dark:bg-zinc-800 dark:text-gray-100 p-3 rounded-md text-base">
			{item.originalText}
		</p>

		<div className="mt-3">
			<Label
				htmlFor={`translation-${index}`}
				className="text-sm font-semibold text-card-foreground dark:text-gray-200"
			>
				<Translate text="paragraphs.your_translation_label" />
				<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
					<Translate text={getTranslationKeyOfLanguage(collection.target_language)} />
				</span>
			</Label>

			<Textarea
				id={`translation-${index}`}
				autoFocus={index === 0}
				value={item.userTranslation || ''}
				onChange={(e) => onTextAreaChange(index, e)}
				placeholder={t('paragraphs.translation_placeholder')}
				rows={4}
				className="w-full mt-1 p-2 focus:ring-primary focus:ring-2 dark:text-gray-100"
				disabled={item.isEvaluating}
			/>

			<div className="flex justify-end mt-1">
				<Button
					size="sm"
					onClick={() => onEvaluate(index)}
					loading={item.isEvaluating}
					disabled={item.isEvaluating || !item.userTranslation?.trim()}
				>
					<Translate text="paragraphs.evaluate_button" />
				</Button>
			</div>
		</div>

		<EvaluationResult item={item} collection={collection} />
	</div>
);

const PracticeForm = ({
	keywords,
	selectedKeywords,
	onKeywordsChange,
	difficulty,
	onDifficultyChange,
	onGenerate,
	isGenerating,
	onDeleteKeyword,
	onCreateKeyword,
}: {
	keywords: any[];
	selectedKeywords: string[];
	onKeywordsChange: (keywords: string[]) => void;
	difficulty: Difficulty;
	onDifficultyChange: (difficulty: Difficulty) => void;
	onGenerate: () => void;
	isGenerating: boolean;
	onDeleteKeyword: (id: string) => Promise<void>;
	onCreateKeyword: (name: string) => Promise<Keyword>;
}) => (
	<>
		<WordGenerationForm
			keywords={keywords}
			selectedKeywords={selectedKeywords}
			onKeywordsChangeAction={onKeywordsChange}
			onGenerateAction={() => {}}
			generatingLoading={isGenerating}
			onDeleteKeywordAction={onDeleteKeyword}
			onCreateKeywordAction={onCreateKeyword}
			hideGenerateButton={true}
		/>

		<div className="space-y-2">
			<Label>
				<Translate text="difficulty.select_difficulty" />
			</Label>
			<DifficultySelector
				value={difficulty}
				onChange={onDifficultyChange}
				className="w-full sm:w-48"
				disabled={isGenerating}
			/>
		</div>

		<Button
			onClick={onGenerate}
			loading={isGenerating}
			disabled={isGenerating || selectedKeywords.length === 0}
			className="w-full sm:w-auto"
		>
			<Translate text="paragraphs.generate_button" />
		</Button>
	</>
);

export function ParagraphPracticeClient({ params }: { params: { id: string } }) {
	const id = params.id;
	const sharedLogic = useSharedPracticeLogic();
	const collection = sharedLogic.currentCollection;
	const { t } = useTranslation();
	const { generateParagraphs, evaluateTranslation } = useLLM();

	const [practiceItems, setPracticeItems] = useState<ParagraphPracticeItem[]>([]);
	const [isGenerating, setIsGenerating] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	const handleGenerate = useCallback(async () => {
		if (!sharedLogic.currentCollection) {
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('errors.collection_not_loaded_title'),
				description: sharedLogic.t('errors.collection_not_loaded_desc'),
			});
			return;
		}

		if (sharedLogic.selectedKeywords.length === 0) {
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('keywords.no_keywords_selected'),
				description: sharedLogic.t('keywords.select_at_least_one'),
			});
			return;
		}

		setIsGenerating(true);
		setError(null);
		setPracticeItems([]);

		try {
			const keywordNames = sharedLogic.selectedKeywords.map(sharedLogic.getKeywordNameFromId);
			const paragraphs = await generateParagraphs({
				keywords: keywordNames,
				language: sharedLogic.currentCollection.source_language,
				difficulty: sharedLogic.difficulty,
				count: PARAGRAPH_COUNT_FOR_TRANSLATION,
			});

			setPracticeItems(
				paragraphs.map((text) => ({
					originalText: text,
					userTranslation: '',
					isEvaluating: false,
					evaluationResult: undefined,
				}))
			);
		} catch (err) {
			const error = err instanceof Error ? err : new Error(String(err));
			setError(error);
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('paragraphs.generation_failed_title'),
				description: error.message,
			});
		} finally {
			setIsGenerating(false);
		}
	}, [
		sharedLogic.currentCollection,
		sharedLogic.selectedKeywords,
		sharedLogic.getKeywordNameFromId,
		sharedLogic.difficulty,
		generateParagraphs,
		sharedLogic.toast,
		sharedLogic.t,
	]);

	const handleTranslationChange = useCallback(
		(index: number, e: React.ChangeEvent<HTMLTextAreaElement>) => {
			setPracticeItems((items) =>
				items.map((item, i) =>
					i === index ? { ...item, userTranslation: e.target.value } : item
				)
			);
		},
		[]
	);

	const handleEvaluate = useCallback(
		async (index: number) => {
			if (!sharedLogic.currentCollection) {
				sharedLogic.toast({
					variant: 'destructive',
					title: sharedLogic.t('errors.collection_not_loaded_title'),
					description: sharedLogic.t('errors.collection_not_loaded_desc'),
				});
				return;
			}

			const item = practiceItems[index];
			if (!item?.userTranslation?.trim()) {
				sharedLogic.toast({
					variant: 'destructive',
					title: sharedLogic.t('paragraphs.evaluation_error_title'),
					description: sharedLogic.t('paragraphs.translation_missing_desc'),
				});
				return;
			}

			setPracticeItems((items) =>
				items.map((item, i) =>
					i === index
						? { ...item, isEvaluating: true, evaluationResult: undefined }
						: item
				)
			);

			try {
				const result = await evaluateTranslation({
					original_text: item.originalText,
					translated_text: item.userTranslation,
					source_language: sharedLogic.currentCollection.source_language,
					target_language: sharedLogic.currentCollection.target_language,
				});

				setPracticeItems((items) =>
					items.map((item, i) =>
						i === index
							? { ...item, isEvaluating: false, evaluationResult: result }
							: item
					)
				);
			} catch (err) {
				const error = err instanceof Error ? err : new Error(String(err));
				setPracticeItems((items) =>
					items.map((item, i) =>
						i === index
							? { ...item, isEvaluating: false, evaluationResult: null }
							: item
					)
				);
				sharedLogic.toast({
					variant: 'destructive',
					title: sharedLogic.t('paragraphs.evaluation_failed_title'),
					description: error.message,
				});
			}
		},
		[
			practiceItems,
			sharedLogic.currentCollection,
			evaluateTranslation,
			sharedLogic.toast,
			sharedLogic.t,
		]
	);

	return (
		<div className="space-y-6 py-4">
			<h1 className="text-2xl font-semibold">
				<Translate text="collections.tabs.paragraph_practice" />
			</h1>
			<p className="text-muted-foreground">
				<Translate text="paragraphs.practice_description" />
			</p>

			<PracticeForm
				keywords={sharedLogic.keywords}
				selectedKeywords={sharedLogic.selectedKeywords}
				onKeywordsChange={sharedLogic.setSelectedKeywords}
				difficulty={sharedLogic.difficulty}
				onDifficultyChange={sharedLogic.setDifficulty}
				onGenerate={handleGenerate}
				isGenerating={isGenerating}
				onDeleteKeyword={sharedLogic.handleDeleteKeyword}
				onCreateKeyword={
					sharedLogic.handleCreateKeyword as (name: string) => Promise<Keyword>
				}
			/>

			{error && !isGenerating && (
				<p className="text-destructive">
					{t('paragraphs.generation_failed_title')}: {error.message}
				</p>
			)}

			{practiceItems.length > 0 && (
				<div className="space-y-8 mt-6">
					<h3 className="text-lg font-semibold">
						<Translate text="paragraphs.generated_list_title" />
					</h3>
					{practiceItems.map((item, index) => (
						<PracticeItemCard
							key={index}
							item={item}
							index={index}
							totalItems={practiceItems.length}
							collection={collection}
							t={t}
							onTextAreaChange={handleTranslationChange}
							onEvaluate={handleEvaluate}
						/>
					))}
				</div>
			)}
		</div>
	);
}
