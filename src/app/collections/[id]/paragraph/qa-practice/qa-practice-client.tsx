'use client';

import { AnswerEvaluationResult } from '@/backend/services/llm.service';
import { Button } from '@/components/ui/button';
import { DifficultySelector } from '@/components/ui/difficulty-selector';
import { Label } from '@/components/ui/label';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Textarea } from '@/components/ui/textarea';
import { Translate } from '@/components/ui/translate';
import { WordGenerationForm } from '@/components/ui/word-generation-form';
import { useCollections, useLLM } from '@/contexts';
import { useTranslation } from '@/contexts';
import { Collection, Difficulty, Keyword, Language } from '@prisma/client';
import React, { useCallback, useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { CollectionWithDetail } from '@/models';
import { useSharedPracticeLogic } from '../../use-shared-practice-logic';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';

const QUESTION_COUNT_FOR_QA = 3;

export interface UseQAPracticeLogicResult {
	// Keyword related
	keywords: Keyword[];
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	handleCreateKeyword: (name: string) => Promise<Keyword | undefined>;
	handleDeleteKeyword: (id: string) => Promise<void>;
	getKeywordNameFromId: (keywordId: string) => string;

	// Difficulty related
	difficulty: Difficulty;
	setDifficulty: (d: Difficulty) => void;

	// Q&A Practice related
	paragraphForQA: string | null;
	generatedQuestions: string[];
	userAnswers: Record<number, string>;
	qaEvaluationResults: Record<number, AnswerEvaluationResult | null | undefined>; // undefined for loading
	qaLoading: {
		generating: boolean; // For paragraph & questions
		evaluating: boolean; // For all answers
	};
	qaError: Error | null;
	handleGenerateParagraphAndQuestionsForQA: () => Promise<void>;
	handleUserAnswerChange: (index: number, value: string) => void;
	handleEvaluateUserAnswers: () => Promise<void>;
}

export function useQAPracticeLogic(): UseQAPracticeLogicResult {
	const sharedLogic = useSharedPracticeLogic();
	const {
		generateParagraphs: llmGenerateParagraphs,
		generateQuestions: llmGenerateQuestions,
		evaluateAnswers: llmEvaluateAnswers,
		isLoading: isLlmHookLoading,
	} = useLLM();

	const [paragraphForQA, setParagraphForQA] = useState<string | null>(null);
	const [generatedQuestions, setGeneratedQuestions] = useState<string[]>([]);
	const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
	const [qaEvaluationResults, setQaEvaluationResults] = useState<
		Record<number, AnswerEvaluationResult | null | undefined>
	>({});
	const [qaLoading, setQaLoading] = useState({
		generating: false,
		evaluating: false,
	});
	const [qaError, setQaError] = useState<Error | null>(null);

	const handleGenerateParagraphAndQuestionsForQA = useCallback(async () => {
		if (!sharedLogic.currentCollection) {
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('errors.collection_not_loaded_title'),
				description: sharedLogic.t('errors.collection_not_loaded_desc'),
			});
			return;
		}
		if (sharedLogic.selectedKeywords.length === 0) {
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('keywords.no_keywords_selected'),
				description: sharedLogic.t('keywords.select_at_least_one'),
			});
			return;
		}
		setQaLoading((prev) => ({ ...prev, generating: true }));
		setQaError(null);
		setParagraphForQA(null);
		setGeneratedQuestions([]);
		setUserAnswers({});
		setQaEvaluationResults({});

		try {
			const keywordNames = sharedLogic.selectedKeywords.map(sharedLogic.getKeywordNameFromId);
			const paragraphs = await llmGenerateParagraphs({
				keywords: keywordNames,
				language: sharedLogic.currentCollection.target_language,
				difficulty: sharedLogic.difficulty,
				count: 1,
				sentenceCount: 20, // Example: request a paragraph of certain length
			});

			if (!paragraphs || paragraphs.length === 0 || !paragraphs[0]) {
				throw new Error(
					sharedLogic.t('qa_practice.errors.paragraph_generation_failed_empty')
				);
			}
			const currentParagraph = paragraphs[0];
			setParagraphForQA(currentParagraph);

			const questions = await llmGenerateQuestions({
				paragraph: currentParagraph,
				language: sharedLogic.currentCollection.target_language,
				questionCount: QUESTION_COUNT_FOR_QA,
			});
			setGeneratedQuestions(questions);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			setQaError(err);
			setParagraphForQA(null);
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('qa_practice.errors.generation_failed_title'),
				description: err.message,
			});
		} finally {
			setQaLoading((prev) => ({ ...prev, generating: false }));
		}
	}, [sharedLogic, llmGenerateParagraphs, llmGenerateQuestions]);

	const handleUserAnswerChange = useCallback((index: number, value: string) => {
		setUserAnswers((prev) => ({ ...prev, [index]: value }));
	}, []);

	const handleEvaluateUserAnswers = useCallback(async () => {
		if (!sharedLogic.currentCollection) {
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('errors.collection_not_loaded_title'),
				description: sharedLogic.t('errors.collection_not_loaded_desc'),
			});
			return;
		}
		if (!paragraphForQA || generatedQuestions.length === 0) {
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('qa_practice.errors.evaluation_error_title'),
				description: sharedLogic.t('qa_practice.errors.no_paragraph_or_questions'),
			});
			return;
		}
		const answersArray = generatedQuestions.map((_, index) => userAnswers[index] || '');
		if (answersArray.some((answer) => !answer.trim())) {
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('qa_practice.errors.evaluation_error_title'),
				description: sharedLogic.t('qa_practice.errors.all_answers_required'),
			});
			return;
		}

		setQaLoading((prev) => ({ ...prev, evaluating: true }));
		setQaError(null);
		setQaEvaluationResults((prev) => {
			// Mark all as loading
			const newResults: Record<number, undefined> = {};
			generatedQuestions.forEach((_, idx) => (newResults[idx] = undefined));
			return newResults;
		});

		try {
			const results = await llmEvaluateAnswers({
				paragraph: paragraphForQA,
				questions: generatedQuestions,
				answers: answersArray,
				qna_language: sharedLogic.currentCollection.target_language,
				feedback_native_language: sharedLogic.currentCollection.source_language,
			});
			const resultsMap: Record<number, AnswerEvaluationResult | null> = {};
			results.forEach((result, index) => {
				resultsMap[index] = result;
			});
			setQaEvaluationResults(resultsMap);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			setQaError(err);
			setQaEvaluationResults((prev) => {
				// Mark all as error on general failure
				const newResults: Record<number, null> = {};
				generatedQuestions.forEach((_, idx) => (newResults[idx] = null));
				return newResults;
			});
			sharedLogic.toast({
				variant: 'destructive',
				title: sharedLogic.t('qa_practice.errors.evaluation_failed_title'),
				description: err.message,
			});
		} finally {
			setQaLoading((prev) => ({ ...prev, evaluating: false }));
		}
	}, [
		paragraphForQA,
		generatedQuestions,
		userAnswers,
		sharedLogic.currentCollection,
		llmEvaluateAnswers,
		sharedLogic.toast,
		sharedLogic.t,
	]);

	// Loading state is handled by shared logic

	return {
		keywords: sharedLogic.keywords,
		selectedKeywords: sharedLogic.selectedKeywords,
		setSelectedKeywords: sharedLogic.setSelectedKeywords,
		handleCreateKeyword: sharedLogic.handleCreateKeyword,
		handleDeleteKeyword: sharedLogic.handleDeleteKeyword,
		getKeywordNameFromId: sharedLogic.getKeywordNameFromId,
		difficulty: sharedLogic.difficulty,
		setDifficulty: sharedLogic.setDifficulty,
		paragraphForQA,
		generatedQuestions,
		userAnswers,
		qaEvaluationResults,
		qaLoading,
		qaError,
		handleGenerateParagraphAndQuestionsForQA,
		handleUserAnswerChange,
		handleEvaluateUserAnswers,
	};
}

// Smaller components

function QAGeneratedParagraph({
	paragraph,
	targetLanguage,
}: {
	paragraph: string;
	targetLanguage: Language;
}) {
	return (
		<div className="space-y-4 mt-6 p-4 border rounded-lg shadow-sm bg-card dark:bg-zinc-900">
			<h3 className="text-lg font-semibold">
				<Translate text="qa_practice.generated_paragraph_title" />
				<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
					<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
				</span>
			</h3>
			<p className="whitespace-pre-wrap text-sm p-3 rounded-md bg-muted dark:bg-zinc-800">
				{paragraph}
			</p>
		</div>
	);
}

function QAQuestionFeedback({
	result,
	index,
	collection,
}: {
	result: AnswerEvaluationResult | null | undefined;
	index: number;
	collection: { target_language: Language; source_language: Language };
}) {
	const { t } = useTranslation();

	if (result === null) {
		return (
			<div className="mt-2 p-2 bg-red-100 border-l-4 border-red-500 rounded text-sm text-red-700 dark:bg-red-900/50 dark:border-red-700 dark:text-red-200">
				<Translate text="qa_practice.errors.evaluation_failed_title" />.
			</div>
		);
	}

	if (!result) return null;

	return (
		<div
			className={`mt-2 p-3 rounded-md border-l-4 text-sm
						${
							result?.score !== undefined && result?.score !== null
								? result?.score >= 4
									? 'border-green-500 bg-green-50 text-green-700 dark:border-green-700 dark:bg-green-900/30 dark:text-green-300'
									: result?.score >= 2
									? 'border-yellow-500 bg-yellow-50 text-yellow-700 dark:border-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
									: 'border-red-500 bg-red-50 text-red-700 dark:border-red-700 dark:bg-red-900/30 dark:text-red-300'
								: 'border-gray-500 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-800/30 dark:text-gray-300'
						}
						`}
		>
			<p className="font-semibold">
				<Translate text="qa_practice.feedback_title" />
				{result?.score !== undefined && result?.score !== null && (
					<span className="ml-2">
						(<Translate text="qa_practice.score_label" />: {result.score}/5)
					</span>
				)}
				{result?.is_correct !== undefined && (
					<span
						className={`ml-2 font-normal px-1.5 py-0.5 rounded text-xs ${
							result.is_correct
								? 'bg-green-100 text-green-800 dark:bg-green-800/50 dark:text-green-200'
								: 'bg-red-100 text-red-800 dark:bg-red-800/50 dark:text-red-200'
						}`}
					>
						{result.is_correct ? t('qa_practice.correct') : t('qa_practice.incorrect')}
					</span>
				)}
			</p>
			<div className="mt-1 space-y-2">
				{result?.feedback?.qna_feedback_text && (
					<div>
						<strong className="text-sm">
							<Translate
								text={getTranslationKeyOfLanguage(collection.target_language)}
							/>
							:
						</strong>
						<p className="whitespace-pre-wrap text-sm">
							{result.feedback.qna_feedback_text}
						</p>
					</div>
				)}
				{result?.feedback?.native_feedback_text && (
					<div>
						<strong className="text-sm">
							<Translate
								text={getTranslationKeyOfLanguage(collection.source_language)}
							/>
							:
						</strong>
						<p className="whitespace-pre-wrap text-sm">
							{result.feedback.native_feedback_text}
						</p>
					</div>
				)}
			</div>
			{result?.suggested_answer && (
				<div className="mt-2 pt-2 border-t border-border/50">
					<p className="font-medium">
						<Translate text="qa_practice.suggested_answer_label" />:
					</p>
					<p className="whitespace-pre-wrap">{result.suggested_answer}</p>
				</div>
			)}
		</div>
	);
}

function QAQuestionItem({
	question,
	answer,
	onAnswerChange,
	feedbackResult,
	disabled,
	index,
	collection,
}: {
	question: string;
	answer: string;
	onAnswerChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	feedbackResult: AnswerEvaluationResult | null | undefined;
	disabled: boolean;
	index: number;
	collection: Collection;
}) {
	const { t } = useTranslation();

	return (
		<div className="p-4 border rounded-lg shadow-sm bg-card dark:bg-zinc-900 space-y-3">
			<div className="flex items-start space-x-2">
				<div className="flex-shrink-0">
					<p className="font-semibold text-base">
						<Translate text="qa_practice.question_label" /> {index + 1}:
					</p>
				</div>
				<div className="flex-grow">
					<p className="font-normal text-base">{question}</p>
				</div>
			</div>
			<Label htmlFor={`answer-${index}`} className="sr-only">
				Answer for Question {index + 1}
			</Label>
			<Textarea
				id={`answer-${index}`}
				value={answer || ''}
				onChange={onAnswerChange}
				placeholder={t('qa_practice.answer_placeholder')}
				rows={3}
				className="w-full mt-1 p-3 text-base"
				disabled={disabled}
			/>
			<QAQuestionFeedback result={feedbackResult} index={index} collection={collection} />
		</div>
	);
}

function QAQuestionsSection({
	questions,
	userAnswers,
	onUserAnswerChange,
	qaEvaluationResults,
	collection,
	isOverallLoading,
	handleEvaluateUserAnswers,
	qaLoading,
}: {
	questions: string[];
	userAnswers: Record<number, string>;
	onUserAnswerChange: (index: number, value: string) => void;
	qaEvaluationResults: Record<number, AnswerEvaluationResult | null | undefined>;
	collection: Collection;
	isOverallLoading: boolean;
	handleEvaluateUserAnswers: () => void;
	qaLoading: { evaluating: boolean; generating: boolean };
}) {
	const { t } = useTranslation();

	const allAnswersFilled = questions.every(
		(_, idx) => userAnswers[idx] && userAnswers[idx].trim() !== ''
	);

	return (
		<div className="space-y-6 mt-6">
			<h3 className="text-lg font-semibold">
				<Translate text="qa_practice.questions_title" />
			</h3>
			{questions.map((question, index) => (
				<QAQuestionItem
					key={index}
					question={question}
					answer={userAnswers[index] || ''}
					onAnswerChange={(e) => onUserAnswerChange(index, e.target.value)}
					feedbackResult={qaEvaluationResults[index]}
					disabled={isOverallLoading}
					index={index}
					collection={collection}
				/>
			))}
			<Button
				onClick={handleEvaluateUserAnswers}
				disabled={isOverallLoading || questions.length === 0 || !allAnswersFilled}
				className="w-full sm:w-auto"
			>
				{qaLoading.evaluating ? (
					<LoadingSpinner size="sm" />
				) : (
					<Translate text="qa_practice.buttons.evaluate_answers" />
				)}
			</Button>
		</div>
	);
}

export function QAPracticeClient({ params }: { params: { id: string } }) {
	const id = params.id;
	const sharedLogic = useSharedPracticeLogic();
	const collection = sharedLogic.currentCollection;
	const logic = useQAPracticeLogic();

	if (!collection) {
		return (
			<div className="flex justify-center items-center h-full">
				<LoadingSpinner />
			</div>
		);
	}

	const isOverallLoading = logic.qaLoading.generating || logic.qaLoading.evaluating;

	return (
		<div className="space-y-6 py-4">
			<h1 className="text-2xl font-semibold">
				<Translate text="qa_practice.tab_title" />
			</h1>
			<p className="text-muted-foreground">
				<Translate text="qa_practice.description" />
			</p>

			<WordGenerationForm
				keywords={logic.keywords}
				selectedKeywords={logic.selectedKeywords}
				onKeywordsChangeAction={logic.setSelectedKeywords}
				onGenerateAction={() => {}} // Generate button is separate
				generatingLoading={false} // WordGenerationForm's own loading, not used here
				onDeleteKeywordAction={logic.handleDeleteKeyword}
				onCreateKeywordAction={
					logic.handleCreateKeyword as (name: string) => Promise<Keyword>
				}
				hideGenerateButton={true}
			/>

			<div className="space-y-2">
				<Label>
					<Translate text="difficulty.select_difficulty" />
				</Label>
				<DifficultySelector
					value={logic.difficulty}
					onChange={logic.setDifficulty}
					className="w-full sm:w-48"
				/>
			</div>

			<Button
				onClick={logic.handleGenerateParagraphAndQuestionsForQA}
				disabled={isOverallLoading || logic.selectedKeywords.length === 0}
				className="w-full sm:w-auto"
			>
				{logic.qaLoading.generating ? (
					<LoadingSpinner size="sm" />
				) : (
					<Translate text="qa_practice.buttons.generate_paragraph_questions" />
				)}
			</Button>

			{logic.qaError && !isOverallLoading && (
				<p className="text-destructive">
					{t('qa_practice.errors.generation_failed_title')}: {logic.qaError.message}
				</p>
			)}

			{logic.paragraphForQA && (
				<QAGeneratedParagraph
					paragraph={logic.paragraphForQA}
					targetLanguage={collection.target_language}
				/>
			)}

			{logic.generatedQuestions.length > 0 && (
				<QAQuestionsSection
					questions={logic.generatedQuestions}
					userAnswers={logic.userAnswers}
					onUserAnswerChange={logic.handleUserAnswerChange}
					qaEvaluationResults={logic.qaEvaluationResults}
					collection={collection}
					isOverallLoading={isOverallLoading}
					handleEvaluateUserAnswers={logic.handleEvaluateUserAnswers}
					qaLoading={logic.qaLoading}
				/>
			)}
		</div>
	);
}
