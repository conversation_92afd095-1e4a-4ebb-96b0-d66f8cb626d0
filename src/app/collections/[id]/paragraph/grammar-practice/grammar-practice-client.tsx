'use client';

import { GrammarPracticeResultItem } from '@/backend/services';
import {
	Button,
	DifficultySelector,
	Label,
	LoadingSpinner,
	Translate,
	WordGenerationForm,
} from '@/components/ui';
import { useKeywords, useLLM, useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { cn } from '@/lib';
import { Difficulty, Keyword, Language } from '@prisma/client';
import { useCallback, useState } from 'react';
import { useSharedPracticeLogic } from '../../use-shared-practice-logic';

// Constants
const PARAGRAPH_COUNT = 5;

// Types
type SelectedWord = {
	word: string;
	index: number;
};

// Components
const InteractiveWord = ({
	word,
	isSelected,
	disabled,
	onClick,
}: {
	word: string;
	index: number;
	isSelected: boolean;
	disabled: boolean;
	onClick: () => void;
}) => (
	<span
		className={cn('cursor-pointer transition-colors duration-150 ease-in-out', {
			'bg-yellow-200 dark:bg-yellow-700 hover:bg-yellow-300 dark:hover:bg-yellow-600 rounded px-0.5 py-0.5':
				isSelected,
			'hover:bg-gray-200 dark:hover:bg-zinc-700 rounded px-0.5 py-0.5':
				!isSelected && !disabled,
			'cursor-not-allowed opacity-70': disabled,
		})}
		onClick={() => !disabled && onClick()}
	>
		{word}
	</span>
);

const GrammarPracticeResult = ({
	item,
	show,
}: {
	item: GrammarPracticeResultItem;
	show: boolean;
}) =>
	show ? (
		<div className="mt-4 space-y-4">
			<div className="bg-muted/50 p-4 rounded-lg">
				<h4 className="font-medium mb-2">
					<Translate text="grammar.corrections_label" />
				</h4>
				<p className="text-card-foreground dark:text-gray-300">{item.correctedParagraph}</p>
			</div>
			<div className="bg-muted/50 p-4 rounded-lg">
				<h4 className="font-medium mb-2">
					<Translate text="grammar.explanation_label" />
				</h4>
				<ul className="list-disc ml-6">
					{item.allErrors.map((err, idx) => (
						<li key={idx} className="mb-2">
							<span className="font-semibold text-destructive">{err.errorText}</span>{' '}
							→{' '}
							<span className="font-semibold text-success">{err.correctedText}</span>
							<br />
							<span className="text-xs text-muted-foreground">
								{err.errorType}: {err.explanation}
							</span>
						</li>
					))}
				</ul>
			</div>
		</div>
	) : null;

const GrammarPracticeItem = ({
	item,
	index,
	totalItems,
	selectedWords,
	onWordSelection,
	language,
}: {
	item: GrammarPracticeResultItem;
	index: number;
	totalItems: number;
	selectedWords: SelectedWord[];
	onWordSelection: (paragraphIndex: number, word: string, wordIndex: number) => void;
	language: Language;
}) => {
	const [showResults, setShowResults] = useState(false);
	const words = item.paragraphWithErrors.split(/\s+/);
	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between mb-2">
				<span className="text-sm font-semibold text-card-foreground dark:text-gray-200">
					<Translate text="grammar.original_text_label" /> ({index + 1}/{totalItems}){' '}
					<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
						<Translate text={getTranslationKeyOfLanguage(language)} />
					</span>
				</span>
				<Button variant="outline" size="sm" onClick={() => setShowResults(!showResults)}>
					<Translate
						text={showResults ? 'grammar.hide_results' : 'grammar.show_results'}
					/>
				</Button>
			</div>
			<div className="space-y-2">
				<p className="text-card-foreground dark:text-gray-300 leading-relaxed">
					{words.map((word, wordIndex) => {
						const isSelected = selectedWords.some((s) => s.index === wordIndex);
						const isDisabled = showResults;
						return (
							<InteractiveWord
								key={`${word}-${wordIndex}`}
								word={word}
								index={wordIndex}
								isSelected={isSelected}
								disabled={isDisabled}
								onClick={() => onWordSelection(index, word, wordIndex)}
							/>
						);
					})}
				</p>
				<GrammarPracticeResult item={item} show={showResults} />
			</div>
		</div>
	);
};

const useGrammarPractice = () => {
	const { generateGrammarPractice, isLoading, error: llmError } = useLLM();
	const [paragraphs, setParagraphs] = useState<GrammarPracticeResultItem[]>([]);
	const [selections, setSelections] = useState<Record<number, SelectedWord[]>>({});
	const [generationState, setGenerationState] = useState<{
		loading: boolean;
		error: Error | null;
	}>({ loading: false, error: null });
	const [keywords, setKeywords] = useState<string[]>([]);
	const [difficulty, setDifficulty] = useState<Difficulty>('BEGINNER');

	const generateParagraphs = useCallback(async () => {
		if (!keywords.length) return;
		setGenerationState({ loading: true, error: null });
		setParagraphs([]);
		setSelections({});
		try {
			const result = await generateGrammarPractice({
				keywords,
				language: 'EN', // You may want to get this from context
				difficulty: difficulty as any,
				count: PARAGRAPH_COUNT,
			});
			setParagraphs(result);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			setGenerationState({ loading: false, error: err });
		} finally {
			setGenerationState((prev) => ({ ...prev, loading: false }));
		}
	}, [keywords, difficulty, generateGrammarPractice]);

	const toggleWordSelection = useCallback(
		(paragraphIndex: number, word: string, wordIndex: number) => {
			setSelections((prev) => {
				const currentSelections = prev[paragraphIndex] || [];
				const exists = currentSelections.some((s) => s.index === wordIndex);
				return {
					...prev,
					[paragraphIndex]: exists
						? currentSelections.filter((s) => s.index !== wordIndex)
						: [...currentSelections, { word, index: wordIndex }],
				};
			});
		},
		[]
	);

	return {
		paragraphs,
		selections,
		generationState,
		isLoading,
		llmError,
		keywords,
		setKeywords,
		difficulty,
		setDifficulty,
		generateParagraphs,
		toggleWordSelection,
	};
};

export function GrammarPracticeClient() {
	const { t } = useTranslation();
	const sharedLogic = useSharedPracticeLogic();
	const collection = sharedLogic.currentCollection;
	const { keywords, createKeyword, deleteKeyword } = useKeywords();
	const logic = useGrammarPractice();

	return (
		<div className="space-y-6 py-4">
			<header>
				<h1 className="text-2xl font-semibold">
					<Translate text="collections.tabs.grammar_practice" />
				</h1>
				<p className="text-muted-foreground">
					<Translate text="grammar.practice_description" />
				</p>
			</header>
			<section className="space-y-4">
				<WordGenerationForm
					keywords={keywords}
					selectedKeywords={logic.keywords}
					onKeywordsChangeAction={logic.setKeywords}
					onGenerateAction={() => {}}
					generatingLoading={logic.generationState.loading}
					onDeleteKeywordAction={deleteKeyword}
					onCreateKeywordAction={createKeyword as (name: string) => Promise<Keyword>}
					hideGenerateButton
				/>
				<div className="space-y-2">
					<Label>
						<Translate text="difficulty.select_difficulty" />
					</Label>
					<DifficultySelector
						value={logic.difficulty}
						onChange={logic.setDifficulty}
						className="w-full sm:w-48"
					/>
				</div>
				<Button
					onClick={logic.generateParagraphs}
					disabled={logic.generationState.loading || !logic.keywords.length}
					className="w-full sm:w-auto"
				>
					{logic.generationState.loading ? (
						<LoadingSpinner size="sm" />
					) : (
						<Translate text="grammar.view_result_button" />
					)}
				</Button>
			</section>
			{(logic.generationState.error || logic.llmError) && (
				<p className="text-destructive">
					{t('grammar.generation_failed_title')}:{' '}
					{(logic.generationState.error || logic.llmError)?.message}
				</p>
			)}
			{logic.paragraphs.length > 0 && (
				<section className="space-y-8 mt-6">
					<h3 className="text-lg font-semibold">
						<Translate text="grammar.generated_list_title" />
					</h3>
					{logic.paragraphs.map((item, index) => (
						<GrammarPracticeItem
							key={index}
							item={item}
							index={index}
							totalItems={logic.paragraphs.length}
							selectedWords={logic.selections[index] || []}
							onWordSelection={logic.toggleWordSelection}
							language={collection?.target_language || 'EN'}
						/>
					))}
				</section>
			)}
		</div>
	);
}
