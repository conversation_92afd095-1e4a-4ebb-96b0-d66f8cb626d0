'use client';

import { LoadingSpinner, useToast } from '@/components/ui';
import { FormDialog } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Translate } from '@/components/ui/translate';
import { useTranslation } from '@/contexts';
import { Check, X } from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';

export function RenameCollectionDialog({
	open,
	onOpenChange,
	collection,
	renameCollection,
	renameLoading,
	renameError,
}: {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	collection: { id: string; name: string };
	renameCollection: (params: { name: string }) => Promise<void>;
	renameLoading: boolean;
	renameError?: { message: string } | null;
}) {
	const { t } = useTranslation();
	const { toast } = useToast();
	const [newCollectionName, setNewCollectionName] = useState(collection.name);

	useEffect(() => {
		if (open) {
			setNewCollectionName(collection.name);
		}
	}, [open, collection.name]);

	const handleSubmit = useCallback(async () => {
		if (!newCollectionName.trim() || !collection) return;
		try {
			await renameCollection({ name: newCollectionName.trim() });
			onOpenChange(false);
			toast({
				title: t('collections.rename_success'),
				description: t('collections.rename_success_desc', {
					name: newCollectionName.trim(),
				}),
			});
		} catch (error: any) {
			toast({
				variant: 'destructive',
				title: t('collections.rename_error'),
				description:
					(renameError?.message || error instanceof Error ? error.message : null) ||
					t('collections.rename_error_desc'),
			});
		}
	}, [newCollectionName, collection, renameCollection, onOpenChange, toast, t, renameError]);

	const handleCancel = useCallback(() => {
		onOpenChange(false);
	}, [onOpenChange]);

	return (
		<FormDialog
			open={open}
			onOpenChange={onOpenChange}
			title={<Translate text="collections.edit" />}
			onSubmit={(e) => {
				e.preventDefault();
				handleSubmit();
			}}
			submitText={<Translate text="ui.save" />}
			cancelText={<Translate text="ui.cancel" />}
			submitDisabled={!newCollectionName.trim()}
			loading={renameLoading}
			submitIcon={renameLoading ? <LoadingSpinner size="sm" /> : <Check size={16} />}
			cancelIcon={<X size={16} />}
			onCancel={handleCancel}
		>
			<div className="space-y-4">
				<Input
					value={newCollectionName}
					onChange={(e) => setNewCollectionName(e.target.value)}
					placeholder="Enter new collection name"
					className="bg-background/50 backdrop-blur-sm focus:ring-2 focus:ring-primary/20 transition-all duration-200"
					disabled={renameLoading}
					autoFocus
				/>
			</div>
		</FormDialog>
	);
}
