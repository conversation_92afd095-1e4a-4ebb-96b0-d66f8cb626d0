'use client';

import { useToast } from '@/components/ui/use-toast';
import { useCollections, useTranslation } from '@/contexts';
import { useDebounce } from '@/lib';
import { WordDetail } from '@/models';
import { Definition, Example, Explain, PartsOfSpeech, Word } from '@prisma/client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { EmptyCollection, ListSkeleton, WordCard } from '@/components/ui';
import { Input } from '@/components/ui/input';
import { Translate } from '@/components/ui/translate';
import { motion } from 'framer-motion';
import { ConfirmDialog } from '@/components/ui/dialog';
import { useLoading } from '@/contexts/loading-context';

export function MyWordsClient() {
	const { t } = useTranslation();
	const { currentCollection, removeWordsFromCurrentCollection, refreshCurrentCollection } =
		useCollections();
	const { setLoading: setGlobalLoading, isLoading } = useLoading();
	const { toast } = useToast();
	const {
		currentCollectionWords: collectionWords,
		loading: { wordsSearch: searchLoading, fetchWords: wordsLoading },
		searchWords: wordSearch,
		fetchCurrentCollectionWords: fetchWordsByCollection,
	} = useCollections();

	// State
	const [searchQuery, setSearchQuery] = useState('');
	const collection = currentCollection;
	const [wordActionLoading, setWordActionLoading] = useState<
		Record<string, { removing?: boolean }>
	>({});
	const [wordIdToDelete, setWordIdToDelete] = useState<string | null>(null);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	const debouncedSearchQuery = useDebounce(searchQuery, 300);
	const isSearching = debouncedSearchQuery.trim().length > 0;

	useEffect(() => {
		if (currentCollection && debouncedSearchQuery.trim()) {
			wordSearch(debouncedSearchQuery.trim());
		} else if (currentCollection && !debouncedSearchQuery.trim()) {
			fetchWordsByCollection();
		}
	}, [debouncedSearchQuery, currentCollection, wordSearch, fetchWordsByCollection]);

	useEffect(() => {
		setGlobalLoading(isLoading || searchLoading || wordsLoading);
	}, [isLoading, searchLoading, wordsLoading, setGlobalLoading]);

	// Transform words data
	const wordsToDisplay = useMemo(() => {
		if (!currentCollection || !collection) return [];

		return (collectionWords || []).map(
			(
				word: Word & {
					definitions?: (Definition & {
						explains?: Explain[];
						examples?: Example[];
						images?: string[];
					})[];
				}
			) =>
				({
					id: word.id,
					term: word.term,
					language: word.language,
					audio_url: word.audio_url ?? null,
					source_language: collection.source_language,
					target_language: collection.target_language,
					collection_id: collection.id,
					created_at: word.created_at,
					updated_at: word.updated_at,
					definitions: (word.definitions || []).map((def) => ({
						id: def.id,
						word_id: word.id,
						pos: def.pos as PartsOfSpeech[],
						ipa: def.ipa as string,
						images: def.images || [],
						explains: (def.explains || []).map((ex) => ({
							id: ex.id,
							EN: ex.EN,
							VI: ex.VI,
							definition_id: ex.definition_id,
						})),
						examples: (def.examples || []).map((ex) => ({
							id: ex.id,
							EN: ex.EN,
							VI: ex.VI,
							definition_id: ex.definition_id,
						})),
					})),
				} as WordDetail)
		);
	}, [collectionWords, collection, currentCollection]);

	// Handlers
	const handleDeleteWord = useCallback((wordId: string) => {
		setWordIdToDelete(wordId);
		setIsDeleteDialogOpen(true);
	}, []);

	const confirmDeleteWord = useCallback(async () => {
		if (!wordIdToDelete || !currentCollection) return;

		setWordActionLoading((prev) => ({ ...prev, [wordIdToDelete]: { removing: true } }));

		try {
			await removeWordsFromCurrentCollection([wordIdToDelete]);
			toast({ title: t('collections.remove_word_success') });
			setIsDeleteDialogOpen(false);
			setWordIdToDelete(null);
			await Promise.all([
				fetchWordsByCollection(),
				refreshCurrentCollection(),
			]);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('collections.remove_word_error'),
				description: err.message,
			});
		} finally {
			setWordActionLoading((prev) => ({ ...prev, [wordIdToDelete]: { removing: false } }));
		}
	}, [
		wordIdToDelete,
		currentCollection,
		removeWordsFromCurrentCollection,
		toast,
		t,
		fetchWordsByCollection,
		refreshCurrentCollection,
	]);

	// Loading state
	if (wordsLoading && wordsToDisplay.length === 0 && !searchQuery) {
		return <div>Loading words...</div>;
	}

	// Render content based on state
	const renderContent = () => {
		if (searchLoading) {
			return <ListSkeleton />;
		}

		if (isSearching && wordsToDisplay.length === 0) {
			return (
				<div className="flex justify-center py-8 text-muted-foreground">
					<Translate text="ui.no_results_found" />
				</div>
			);
		}

		if (!isSearching && wordsLoading) {
			return <ListSkeleton />;
		}

		if (!isSearching && wordsToDisplay.length === 0) {
			return <EmptyCollection />;
		}

		if (wordsToDisplay.length > 0) {
			return (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					className="flex flex-col gap-4"
				>
					{wordsToDisplay.map((word) => (
						<WordCard
							key={word.id}
							word={word}
							onDeleteWord={() => handleDeleteWord(word.id)}
							isDeleting={!!wordActionLoading[word.id]?.removing}
						/>
					))}
				</motion.div>
			);
		}

		return null;
	};

	return (
		<>
			<section className="mt-8">
				<div className="mb-8">
					<h2 className="text-xl font-semibold mb-4">
						<Translate text="collections.words" />
					</h2>
					<Input
						type="text"
						placeholder="Search words…"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
					/>
				</div>
				<div className="mt-8">{renderContent()}</div>
			</section>

			<ConfirmDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
				title={<Translate text="collections.remove_word" />}
				description={<Translate text="collections.remove_word_confirm" />}
				onConfirm={confirmDeleteWord}
				confirmText={<Translate text="ui.remove" />}
				cancelText={<Translate text="ui.cancel" />}
				variant="destructive"
				loading={!!(wordIdToDelete && wordActionLoading[wordIdToDelete]?.removing)}
			/>
		</>
	);
}
