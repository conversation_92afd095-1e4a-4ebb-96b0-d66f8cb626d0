'use client';

import { ListSkeleton, RandomWordsList, useToast, WordGenerationForm } from '@/components/ui';
import { CollectionsContextType, useCollections, useKeywords, useLLM } from '@/contexts';
import { useTranslation } from '@/contexts';
import { useLoading } from '@/contexts/loading-context';
import { RandomWord, WordDetail } from '@/models';
import { useCallback, useEffect, useMemo, useState } from 'react';

const MAX_TERMS_TO_GENERATE = 10;

// Types
type WordActionLoading = Record<
	string,
	{ adding?: boolean; gettingDetail?: boolean; error?: Error | null }
>;
type GenerationLoadingState = { generating: boolean; error: Error | null };

// Custom hooks
function useWordActionLoading() {
	const [wordActionLoading, setWordActionLoading] = useState<WordActionLoading>({});

	const setWordLoadingState = useCallback(
		(
			wordIdentifier: string,
			action: 'adding' | 'gettingDetail',
			value: boolean | Error | null
		) => {
			setWordActionLoading((prev) => {
				const newActionState = { ...(prev[wordIdentifier] || {}) };
				if (typeof value === 'boolean') {
					newActionState[action] = value;
					if (value) newActionState.error = null;
				} else if (value instanceof Error) {
					newActionState.error = value;
					newActionState[action] = false;
				} else {
					newActionState.error = null;
				}
				return { ...prev, [wordIdentifier]: newActionState };
			});
		},
		[]
	);

	return { wordActionLoading, setWordLoadingState };
}

function useKeywordManagement(id: string) {
	const { t } = useTranslation();
	const { toast } = useToast();
	const {
		keywords,
		createKeyword,
		deleteKeyword: originalDeleteKeyword,
		fetchKeywords,
		isLoading: keywordsLoadingState,
	} = useKeywords();
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);

	useEffect(() => {
		if (id) fetchKeywords();
	}, [fetchKeywords, id]);

	const keywordIdToNameMap = useMemo(() => {
		const map: Record<string, string> = {};
		(keywords || []).forEach((k) => (map[k.id] = k.content));
		return map;
	}, [keywords]);

	const getKeywordNameFromId = useCallback(
		(keywordId: string): string => keywordIdToNameMap[keywordId] || keywordId,
		[keywordIdToNameMap]
	);

	const handleCreateKeyword = useCallback(
		async (name: string) => {
			try {
				const newKeyword = await createKeyword(name);
				if (!newKeyword) throw new Error(t('keywords.create_failed'));
				toast({
					title: t('keywords.created_title'),
					description: t('keywords.created_desc', { name }),
				});
				return newKeyword;
			} catch (error: unknown) {
				toast({
					variant: 'destructive',
					title: t('keywords.create_failed_title'),
					description: error instanceof Error ? error.message : String(error),
				});
				throw error;
			}
		},
		[createKeyword, toast, t]
	);

	const handleDeleteKeyword = useCallback(
		async (keywordId: string) => {
			try {
				await originalDeleteKeyword(keywordId);
				setSelectedKeywords((prev) => prev.filter((id) => id !== keywordId));
				toast({ title: t('keywords.deleted_title') });
			} catch (error: unknown) {
				toast({
					variant: 'destructive',
					title: t('keywords.delete_failed_title'),
					description: error instanceof Error ? error.message : String(error),
				});
			}
		},
		[originalDeleteKeyword, toast, t]
	);

	return {
		keywords,
		keywordsLoadingState,
		selectedKeywords,
		setSelectedKeywords,
		getKeywordNameFromId,
		handleCreateKeyword,
		handleDeleteKeyword,
	};
}

function useWordGeneration(
	id: string,
	collection: any,
	selectedKeywords: string[],
	getKeywordNameFromId: (id: string) => string
) {
	const { t } = useTranslation();
	const { toast } = useToast();
	const { generateRandomTerms } = useLLM();
	const [randomWords, setRandomWords] = useState<RandomWord[]>([]);
	const [generationLoadingState, setGenerationLoadingState] = useState<GenerationLoadingState>({
		generating: false,
		error: null,
	});

	const handleGenerateWords = useCallback(async () => {
		if (!collection || !id) {
			toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
			return;
		}
		if (selectedKeywords.length === 0) {
			toast({
				variant: 'destructive',
				title: t('keywords.no_keywords_selected'),
				description: t('keywords.select_at_least_one'),
			});
			return;
		}
		setGenerationLoadingState({ generating: true, error: null });
		try {
			const keywordNames = selectedKeywords.map(getKeywordNameFromId);
			const generatedTerms = await generateRandomTerms({
				keywords: keywordNames,
				max_terms: MAX_TERMS_TO_GENERATE,
				exclude_collection_ids: [id],
				source_language: collection.source_language,
				target_language: collection.target_language,
			});
			setRandomWords(generatedTerms);
		} catch (error: unknown) {
			const err = error instanceof Error ? error : new Error(String(error));
			setGenerationLoadingState({ generating: false, error: err });
			toast({
				variant: 'destructive',
				title: t('words.generation_failed'),
				description: err.message,
			});
		} finally {
			setGenerationLoadingState((prev) => ({ ...prev, generating: false }));
		}
	}, [selectedKeywords, getKeywordNameFromId, generateRandomTerms, collection, id, toast, t]);

	return {
		randomWords,
		setRandomWords,
		generationLoadingState,
		handleGenerateWords,
	};
}

function useWordDetails(
	collection: any,
	setWordLoadingState: (
		wordIdentifier: string,
		action: 'adding' | 'gettingDetail',
		value: boolean | Error | null
	) => void
) {
	const { t } = useTranslation();
	const { toast } = useToast();
	const { generateWordDetails } = useLLM();
	const [detailedWords, setDetailedWords] = useState<Record<string, WordDetail>>({});

	const handleGetDetails = useCallback(
		async (word: RandomWord, wordActionLoading: WordActionLoading) => {
			if (!collection) {
				toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
				return;
			}
			if (wordActionLoading[word.term]?.gettingDetail || detailedWords[word.term]) return;
			setWordLoadingState(word.term, 'gettingDetail', true);
			try {
				const detailsList = await generateWordDetails(
					[word.term],
					collection.source_language,
					collection.target_language
				);
				if (detailsList && detailsList.length > 0) {
					setDetailedWords((prev) => ({
						...prev,
						[word.term]: detailsList[0] as WordDetail,
					}));
				} else {
					throw new Error(t('words.detail_fetch_no_data', { term: word.term }));
				}
			} catch (error: unknown) {
				const err = error instanceof Error ? error : new Error(String(error));
				setWordLoadingState(word.term, 'gettingDetail', err);
				toast({
					variant: 'destructive',
					title: t('words.detail_fetch_error'),
					description: err.message,
				});
			} finally {
				setWordLoadingState(word.term, 'gettingDetail', false);
			}
		},
		[generateWordDetails, collection, toast, t, setWordLoadingState, detailedWords]
	);

	return { detailedWords, setDetailedWords, handleGetDetails };
}

function useWordActions(
	id: string,
	collection: any,
	detailedWords: Record<string, WordDetail>,
	setWordLoadingState: (
		wordIdentifier: string,
		action: 'adding' | 'gettingDetail',
		value: boolean | Error | null
	) => void,
	setRandomWords: React.Dispatch<React.SetStateAction<RandomWord[]>>,
	setDetailedWords: React.Dispatch<React.SetStateAction<Record<string, WordDetail>>>
) {
	const { t } = useTranslation();
	const { toast } = useToast();
	const collectionsContext: CollectionsContextType = useCollections();
	const { addTermToCurrentCollection, addWordsToCurrentCollection, refreshCurrentCollection } =
		collectionsContext;

	const handleAddToCollection = useCallback(
		async (word: RandomWord, wordActionLoading: WordActionLoading) => {
			if (!collection || !id) {
				toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
				return;
			}
			if (wordActionLoading[word.term]?.adding) return;
			setWordLoadingState(word.term, 'adding', true);
			try {
				if (detailedWords[word.term]?.id) {
					await addWordsToCurrentCollection([detailedWords[word.term].id]);
				} else {
					await addTermToCurrentCollection(word.term, collection.target_language);
				}
				toast({
					title: t('words.word_added'),
					description: t('words.word_added_desc', { term: word.term }),
				});
				await refreshCurrentCollection();

				setRandomWords((prev) => prev.filter((rw) => rw.term !== word.term));
				setDetailedWords((prev) => {
					const newDetails = { ...prev };
					delete newDetails[word.term];
					return newDetails;
				});
			} catch (error: unknown) {
				const err = error instanceof Error ? error : new Error(String(error));
				setWordLoadingState(word.term, 'adding', err);
				toast({
					variant: 'destructive',
					title: t('words.add_error'),
					description: t('words.add_error_desc', {
						term: word.term,
						message: err.message,
					}),
				});
			} finally {
				setWordLoadingState(word.term, 'adding', false);
			}
		},
		[
			addWordsToCurrentCollection,
			addTermToCurrentCollection,
			collection,
			id,
			toast,
			t,
			refreshCurrentCollection,
			setWordLoadingState,
			detailedWords,
			setRandomWords,
			setDetailedWords,
		]
	);

	return { handleAddToCollection };
}

// Main component
export function GenerateWordsClient({ params }: { params: { id: string } }) {
	const { currentCollection, loading, error } = useCollections();
	const id = params.id;
	const collection = currentCollection;
	const isLoading = loading.get || loading.setCurrent;
	const { setLoading: setGlobalLoading } = useLoading();
	const { isLoading: isLlmHookLoading } = useLLM();

	// Custom hooks
	const { wordActionLoading, setWordLoadingState } = useWordActionLoading();
	const {
		keywords,
		keywordsLoadingState,
		selectedKeywords,
		setSelectedKeywords,
		getKeywordNameFromId,
		handleCreateKeyword,
		handleDeleteKeyword,
	} = useKeywordManagement(id);
	const { randomWords, setRandomWords, generationLoadingState, handleGenerateWords } =
		useWordGeneration(id, collection, selectedKeywords, getKeywordNameFromId);
	const { detailedWords, setDetailedWords, handleGetDetails } = useWordDetails(
		collection,
		setWordLoadingState
	);
	const { handleAddToCollection } = useWordActions(
		id,
		collection,
		detailedWords,
		setWordLoadingState,
		setRandomWords,
		setDetailedWords
	);

	// Global loading effect
	useEffect(() => {
		if (!collection && !id) return;
		const anyLoading =
			generationLoadingState.generating || keywordsLoadingState || isLlmHookLoading;
		setGlobalLoading(anyLoading);
	}, [
		generationLoadingState.generating,
		keywordsLoadingState,
		isLlmHookLoading,
		setGlobalLoading,
		collection,
		id,
	]);

	// Computed values
	const keywordsData = collection ? keywords || [] : [];
	const detailLoadingMap = Object.fromEntries(
		randomWords.map((w) => [w.term, !!wordActionLoading[w.term]?.gettingDetail])
	);
	const addToCollectionLoadingMap = Object.fromEntries(
		randomWords.map((w) => [w.term, !!wordActionLoading[w.term]?.adding])
	);

	// Event handlers with proper binding
	const handleGetDetailsWithLoading = useCallback(
		(word: RandomWord) => handleGetDetails(word, wordActionLoading),
		[handleGetDetails, wordActionLoading]
	);

	const handleAddToCollectionWithLoading = useCallback(
		(word: RandomWord) => handleAddToCollection(word, wordActionLoading),
		[handleAddToCollection, wordActionLoading]
	);

	return (
		<section className="mb-8">
			<WordGenerationForm
				keywords={keywordsData}
				selectedKeywords={selectedKeywords}
				onKeywordsChangeAction={setSelectedKeywords}
				onGenerateAction={handleGenerateWords}
				generatingLoading={generationLoadingState.generating}
				onDeleteKeywordAction={handleDeleteKeyword}
				onCreateKeywordAction={handleCreateKeyword}
			/>

			{generationLoadingState.generating && (
				<div className="mt-8">
					<ListSkeleton />
				</div>
			)}

			{!generationLoadingState.generating && randomWords.length > 0 && collection && (
				<div className="mt-8">
					<RandomWordsList
						loading={generationLoadingState.generating}
						words={randomWords}
						detailedWords={detailedWords}
						detailLoading={detailLoadingMap}
						collection={collection}
						onGetDetails={handleGetDetailsWithLoading}
						onAddToCollection={handleAddToCollectionWithLoading}
						addToCollectionLoading={addToCollectionLoadingMap}
					/>
				</div>
			)}
		</section>
	);
}
