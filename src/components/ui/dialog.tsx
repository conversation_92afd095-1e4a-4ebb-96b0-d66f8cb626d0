import { cn } from '@/lib';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { XIcon } from 'lucide-react';
import React from 'react';
import { Button } from './button';

function Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {
	return <DialogPrimitive.Root data-slot="dialog" {...props} />;
}

function DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {
	return <DialogPrimitive.Trigger data-slot="dialog-trigger" {...props} />;
}

function DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {
	return <DialogPrimitive.Portal data-slot="dialog-portal" {...props} />;
}

function DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {
	return <DialogPrimitive.Close data-slot="dialog-close" {...props} />;
}

function DialogOverlay({
	className,
	...props
}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {
	return (
		<DialogPrimitive.Overlay
			data-slot="dialog-overlay"
			className={cn(
				'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',
				className
			)}
			{...props}
		/>
	);
}

function DialogContent({
	className,
	children,
	'aria-labelledby': ariaLabelledbyProp,
	'aria-describedby': ariaDescribedbyProp,
	...restProps
}: React.ComponentProps<typeof DialogPrimitive.Content> & {
	'aria-labelledby'?: string;
	'aria-describedby'?: string;
}) {
	const contentRef = React.useRef<HTMLDivElement>(null);
	const fallbackTitleId = React.useId();

	// Check if children contain a DialogTitle
	const hasDialogTitle = React.Children.toArray(children).some((child) => {
		if (React.isValidElement(child)) {
			// Check if it's a DialogTitle directly
			if (child.type === DialogTitle) return true;
			// Check if it's a DialogHeader containing DialogTitle
			if (
				child.type === DialogHeader &&
				child.props &&
				typeof child.props === 'object' &&
				'children' in child.props
			) {
				const headerProps = child.props as { children: React.ReactNode };
				return React.Children.toArray(headerProps.children).some(
					(headerChild) =>
						React.isValidElement(headerChild) && headerChild.type === DialogTitle
				);
			}
		}
		return false;
	});

	const primitiveContentProps: { [key: string]: any } & React.RefAttributes<HTMLDivElement> = {
		...restProps,
		ref: contentRef,
		'data-slot': 'dialog-content',
		className: cn(
			'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',
			className
		),
		role: 'dialog',
		'aria-modal': 'true',
		tabIndex: -1,
	};

	// Use provided aria-labelledby or fallback to generated ID if no DialogTitle is found
	if (typeof ariaLabelledbyProp === 'string' && ariaLabelledbyProp.length > 0) {
		primitiveContentProps['aria-labelledby'] = ariaLabelledbyProp;
	} else if (!hasDialogTitle) {
		primitiveContentProps['aria-labelledby'] = fallbackTitleId;
	}

	if (typeof ariaDescribedbyProp === 'string' && ariaDescribedbyProp.length > 0) {
		primitiveContentProps['aria-describedby'] = ariaDescribedbyProp;
	}

	return (
		<DialogPortal data-slot="dialog-portal">
			<DialogOverlay />
			<DialogPrimitive.Content {...primitiveContentProps}>
				{!hasDialogTitle && !ariaLabelledbyProp && (
					<DialogHeader>
						<DialogTitle id={fallbackTitleId} className="sr-only">
							Dialog
						</DialogTitle>
					</DialogHeader>
				)}
				{children}
				<DialogPrimitive.Close
					className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"
					aria-label="Close dialog"
				>
					<XIcon />
					<span className="sr-only">Close</span>
				</DialogPrimitive.Close>
			</DialogPrimitive.Content>
		</DialogPortal>
	);
}

function DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {
	return (
		<div
			data-slot="dialog-header"
			className={cn('flex flex-col gap-2 text-center sm:text-left', className)}
			{...props}
		/>
	);
}

function DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {
	return (
		<div
			data-slot="dialog-footer"
			className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}
			{...props}
		/>
	);
}

function DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {
	return (
		<DialogPrimitive.Title
			data-slot="dialog-title"
			className={cn('text-lg leading-none font-semibold', className)}
			{...props}
		/>
	);
}

function DialogDescription({
	className,
	...props
}: React.ComponentProps<typeof DialogPrimitive.Description>) {
	return (
		<DialogPrimitive.Description
			data-slot="dialog-description"
			className={cn('text-muted-foreground text-sm', className)}
			{...props}
		/>
	);
}

interface ConfirmDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	title: React.ReactNode;
	description: React.ReactNode;
	onConfirm: () => void;
	confirmText?: React.ReactNode;
	cancelText?: React.ReactNode;
	variant?: 'default' | 'destructive';
	loading?: boolean;
}

function ConfirmDialog({
	open,
	onOpenChange,
	title,
	description,
	onConfirm,
	confirmText = 'Confirm',
	cancelText = 'Cancel',
	variant = 'default',
	loading = false,
}: ConfirmDialogProps) {
	const titleId = React.useId();
	const descriptionId = React.useId();

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent aria-labelledby={titleId} aria-describedby={descriptionId}>
				<DialogHeader>
					<DialogTitle id={titleId}>{title}</DialogTitle>
					<DialogDescription id={descriptionId}>{description}</DialogDescription>
				</DialogHeader>
				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={loading}
					>
						{cancelText}
					</Button>
					<Button onClick={onConfirm} variant={variant} disabled={loading}>
						{confirmText}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}

interface FormDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	title: React.ReactNode;
	children: React.ReactNode;
	onSubmit?: (e: React.FormEvent) => void;
	onCancel?: () => void;
	submitText?: React.ReactNode;
	cancelText?: React.ReactNode;
	submitDisabled?: boolean;
	loading?: boolean;
	className?: string;
	footerClassName?: string;
	submitVariant?: 'default' | 'destructive';
	submitIcon?: React.ReactNode;
	cancelIcon?: React.ReactNode;
}

function FormDialog({
	open,
	onOpenChange,
	title,
	children,
	onSubmit,
	onCancel,
	submitText = 'Submit',
	cancelText = 'Cancel',
	submitDisabled = false,
	loading = false,
	className,
	footerClassName,
	submitVariant = 'default',
	submitIcon,
	cancelIcon,
}: FormDialogProps) {
	const titleId = React.useId();

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				aria-labelledby={titleId}
				className={cn('bg-background/80 backdrop-blur-sm', className)}
			>
				<DialogHeader>
					<DialogTitle id={titleId}>{title}</DialogTitle>
				</DialogHeader>
				{onSubmit ? (
					<form onSubmit={onSubmit}>
						{children}
						<DialogFooter className={cn('mt-4', footerClassName)}>
							<Button
							type="button"
							variant="outline"
							onClick={() => {
								onCancel?.();
								onOpenChange(false);
							}}
							className="hover:bg-destructive/10 hover:text-destructive transition-colors duration-200"
							disabled={loading}
						>
								{cancelIcon}
								{cancelText}
							</Button>
							<Button
								type="submit"
								variant={submitVariant}
								className={
									submitVariant === 'default'
										? 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300'
										: ''
								}
								disabled={submitDisabled || loading}
							>
								{submitIcon}
								{submitText}
							</Button>
						</DialogFooter>
					</form>
				) : (
					<>
						{children}
						<DialogFooter className={cn('mt-4', footerClassName)}>
							<Button
					variant="outline"
					onClick={() => {
						onCancel?.();
						onOpenChange(false);
					}}
					className="hover:bg-destructive/10 hover:text-destructive transition-colors duration-200"
					disabled={loading}
				>
								{cancelIcon}
								{cancelText}
							</Button>
						</DialogFooter>
					</>
				)}
			</DialogContent>
		</Dialog>
	);
}

export {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogOverlay,
	DialogPortal,
	DialogTitle,
	DialogTrigger,
	FormDialog,
	ConfirmDialog,
};
