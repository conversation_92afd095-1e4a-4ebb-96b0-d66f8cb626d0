'use client';

import { <PERSON><PERSON>, <PERSON>, LoadingSpinner, MultiSelect, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { Keyword } from '@prisma/client';
import { <PERSON>rk<PERSON>, X } from 'lucide-react';
import { useCallback, useMemo } from 'react';

interface WordGenerationFormProps {
	keywords: Keyword[];
	selectedKeywords: string[];
	onKeywordsChangeAction: (values: string[]) => void;
	onGenerateAction: () => void;
	generatingLoading: boolean;
	onDeleteKeywordAction: (id: string) => Promise<void>;
	onCreateKeywordAction: (name: string) => Promise<Keyword>;
	hideGenerateButton?: boolean;
}

export function WordGenerationForm({
	keywords,
	selectedKeywords,
	onKeywordsChangeAction,
	onGenerateAction,
	generatingLoading,
	onDeleteKeywordAction,
	onCreateKeywordAction,
	hideGenerateButton = false,
}: WordGenerationFormProps) {
	const { t } = useTranslation();
	const keywordOptions = useMemo(
		() =>
			keywords.map((kw) => ({
				label: kw.content,
				value: kw.id,
				action: (
					<Button
						variant="ghost"
						size="sm"
						className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive"
						onClick={async (e) => {
							e.stopPropagation();
							try {
								await onDeleteKeywordAction(kw.id);
							} catch (error) {
								console.error('Failed to delete keyword:', error);
							}
						}}
					>
						<X className="h-3 w-3" />
					</Button>
				),
			})),
		[keywords, onDeleteKeywordAction]
	);

	const handleKeywordsChange = useCallback(
		async (values: string[]) => {
			const newKeywords = values.filter((value) => value.startsWith('temp_'));
			let updatedValues = [...values];
			if (newKeywords.length > 0) {
				for (const value of newKeywords) {
					const keywordName = value.split('_').slice(2).join('_');
					try {
						const newKeyword = await onCreateKeywordAction(keywordName);
						updatedValues = updatedValues.map((v) => (v === value ? newKeyword.id : v));
					} catch (error) {
						console.error('Failed to create keyword:', error);
					}
				}
			}
			onKeywordsChangeAction(updatedValues);
		},
		[onCreateKeywordAction, onKeywordsChangeAction]
	);

	return (
		<Card className="p-6 shadow-lg hover:shadow-xl transition-shadow">
			<div className="flex flex-col space-y-6">
				<div className="flex items-center justify-between">
					<h2 className="text-xl font-semibold flex items-center">
						<Sparkles className="h-5 w-5 text-primary mr-2" />
						<Translate text="words.generate" />
					</h2>
				</div>

				<div>
					<label className="block text-sm font-medium mb-2">
						<Translate text="words.select_keywords" />
					</label>
					<MultiSelect
						options={keywordOptions}
						selectedValues={selectedKeywords}
						onChange={handleKeywordsChange}
						placeholder={t('words.keywords_placeholder')}
						allowNewOptions={true}
					/>
				</div>

				{!hideGenerateButton && (
					<Button
						className="w-full"
						disabled={generatingLoading || selectedKeywords.length === 0}
						onClick={onGenerateAction}
					>
						{generatingLoading ? (
							<>
								<LoadingSpinner size="sm" />
								<Translate text="ui.loading" />
							</>
						) : (
							<>
								<Sparkles className="h-4 w-4" />
								<Translate text="words.generate_words" />
							</>
						)}
					</Button>
				)}
			</div>
		</Card>
	);
}
