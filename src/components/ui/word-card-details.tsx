import { RandomWord, WordDetail } from '@/models';
import { WordCardDefinition } from './word-card-definition';
import { WordCardMeaning } from './word-card-meaning';

interface WordCardDetailsProps {
	detailedWord?: WordDetail;
	word: RandomWord;
	detailLoading: boolean;
	onGetDetails: () => void;
}

export function WordCardDetails({
	detailedWord,
	word,
	detailLoading,
	onGetDetails,
}: WordCardDetailsProps) {
	return (
		<div className="space-y-6">
			{detailedWord ? (
				detailedWord.definitions.map((def, defIndex) => (
					<WordCardDefinition def={def} defIndex={defIndex} key={defIndex} />
				))
			) : (
				<WordCardMeaning
					word={word}
					detailLoading={detailLoading}
					onGetDetails={onGetDetails}
				/>
			)}
		</div>
	);
}
