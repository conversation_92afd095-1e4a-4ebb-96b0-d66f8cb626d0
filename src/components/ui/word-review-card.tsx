'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Card<PERSON>eader, Card<PERSON>itle, Translate } from '@/components/ui';
import { useLastSeenWord } from '@/contexts';
import { cn } from '@/lib';
import { ReviewWordDetail } from '@/models';
import { Word, Definition, Explain, Example } from '@prisma/client';
import { CheckCircle, Eye, Loader2, Volume2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface WordReviewCardProps {
	reviewWord: ReviewWordDetail;
	onMarkAsSeen?: (word: Word) => void;
	collectionId?: string;
}

// Keep track of revealed words across re-renders
const revealedWordIds = new Set<string>();

export function WordReviewCard({ reviewWord, onMarkAsSeen, collectionId }: WordReviewCardProps) {
	const { word } = reviewWord;
	const wordId = word.id;

	const [isLoading, setIsLoading] = useState(false);
	const [isRevealed, setIsRevealed] = useState(revealedWordIds.has(wordId));
	const [showExamples, setShowExamples] = useState(false);
	const { saveWord: actualSaveFunction } = useLastSeenWord();

	// Update the revealed state when word changes
	useEffect(() => {
		setIsRevealed(revealedWordIds.has(wordId));
		setShowExamples(false);
	}, [wordId]);

	const handleMarkAsSeen = async () => {
		try {
			setIsLoading(true);
			await actualSaveFunction(word.id);
			onMarkAsSeen?.(word);
		} catch (error) {
			console.error('Error marking word as seen:', error);
		} finally {
			setIsLoading(false);
		}
	};

	const toggleReveal = () => {
		const newState = !isRevealed;
		setIsRevealed(newState);

		// Store revealed state by word ID
		if (newState) {
			revealedWordIds.add(wordId);
		} else {
			revealedWordIds.delete(wordId);
		}
	};

	const toggleExamples = () => {
		setShowExamples(!showExamples);
	};

	const playPronunciation = () => {
		if (!word.audio_url) return;

		const audio = new Audio(word.audio_url);
		audio.play().catch((e) => console.error('Failed to play audio:', e));
	};

	// Calculate progress indicators based on review scores
	const retentionPercent = Math.round(reviewWord.retention_score * 100);
	const priorityPercent = Math.round(reviewWord.priority_score * 100);

	// Format last seen date if it exists
	const lastSeen = reviewWord.last_seen_at
		? new Date(reviewWord.last_seen_at).toLocaleDateString()
		: 'Never';

	return (
		<Card className="relative">
			<CardHeader>
				<div className="flex justify-between items-center">
					<CardTitle>{word.term}</CardTitle>
					{word.audio_url && (
						<Button
							variant="ghost"
							size="sm"
							onClick={playPronunciation}
							className="ml-2"
						>
							<Volume2 className="h-4 w-4" />
						</Button>
					)}
				</div>
				{(reviewWord.retention_score > 0 || reviewWord.priority_score > 0) && (
					<div className="text-xs text-muted-foreground mt-2">
						<div className="flex items-center gap-2 mb-1">
							<span>Retention:</span>
							<div className="w-full bg-gray-200 rounded-full h-1.5">
								<div
									className="bg-green-500 h-1.5 rounded-full"
									style={{ width: `${retentionPercent}%` }}
								></div>
							</div>
							<span>{retentionPercent}%</span>
						</div>
						<div className="flex items-center gap-2">
							<span>Priority:</span>
							<div className="w-full bg-gray-200 rounded-full h-1.5">
								<div
									className="bg-blue-500 h-1.5 rounded-full"
									style={{ width: `${priorityPercent}%` }}
								></div>
							</div>
							<span>{priorityPercent}%</span>
						</div>
						<div className="mt-1">Last seen: {lastSeen}</div>
					</div>
				)}
			</CardHeader>
			<CardContent>
				<div className={cn('flex flex-col gap-2', !isRevealed && 'blur-md select-none')}>
					{word.definitions &&
						word.definitions.map(
							(
								def: Definition & { explains: Explain[]; examples: Example[] },
								index: number
							) => (
								<div key={index} className="flex flex-col gap-1">
									<div className="text-sm text-gray-500">
										{Array.isArray(def.pos) ? def.pos.join(', ') : def.pos}
									</div>
									<div className="text-sm">{def.ipa}</div>
									{def.explains &&
										def.explains.map(
											(explain: { EN: string; VI: string }, idx: number) => (
												<div key={idx} className="text-sm">
													{explain.EN}
												</div>
											)
										)}

									{def.examples && def.examples.length > 0 && (
										<div className="mt-2 pl-2 border-l-2 border-gray-200">
											{def.examples.map(
												(
													example: { EN: string; VI: string },
													idx: number
												) => (
													<p
														key={idx}
														className="text-sm text-gray-600 mb-1"
													>
														{example.EN}
													</p>
												)
											)}
										</div>
									)}
								</div>
							)
						)}

					<Button
						onClick={handleMarkAsSeen}
						disabled={isLoading}
						variant="outline"
						className="mt-4"
					>
						{isLoading ? (
							<>
								<Loader2 size={16} className="mr-2 animate-spin" />
								<Translate text="review.loading" />
							</>
						) : (
							<>
								<CheckCircle size={16} className="mr-2" />
								<Translate text="review.mark_seen" />
							</>
						)}
					</Button>
				</div>

				{!isRevealed && (
					<div className="absolute inset-0 flex items-center justify-center">
						<Button
							variant="outline"
							size="lg"
							className="rounded-full h-16 w-16 shadow-lg"
							onClick={toggleReveal}
						>
							<Eye className="h-6 w-6" />
						</Button>
					</div>
				)}

				{isRevealed && (
					<Button
						variant="ghost"
						className="absolute top-2 right-2"
						onClick={toggleReveal}
					>
						<Eye className="h-4 w-4" />
					</Button>
				)}
			</CardContent>
		</Card>
	);
}
