import { But<PERSON>, LoadingSpinner } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { Collection } from '@prisma/client';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import React from 'react';

interface WordCardHeaderProps {
	word: { term: string; partOfSpeech?: string | string[] };
	expanded: boolean;
	collections: Collection[];
	selectedCollectionIds: string[];
	onToggleExpand: () => void;
	onChangeCollections: (ids: string[]) => void;
	addToCollectionLoading: boolean;
	localLoading: boolean;
}

export function WordCardHeader({
	word,
	expanded,
	collections,
	onToggleExpand,
	onChangeCollections,
	addToCollectionLoading,
	localLoading,
}: WordCardHeaderProps) {
	const { t } = useTranslation();
	const [added, setAdded] = React.useState(false);

	const handleAddToCollection = async () => {
		if (addToCollectionLoading || localLoading) return;
		if (!collections || collections.length === 0) return;
		await onChangeCollections([collections[0].id]);
		setAdded(true);
	};

	return (
		<div className="flex items-center justify-between">
			<div className="flex-1 flex items-center space-x-4">
				<Button variant="ghost" size="icon" onClick={onToggleExpand} className="h-8 w-8">
					<motion.div
						animate={{ rotate: expanded ? 180 : 0 }}
						transition={{ duration: 0.2 }}
					>
						<ChevronDown className="h-4 w-4" />
					</motion.div>
				</Button>
				<div className="flex items-center space-x-2">
					<div className="space-y-1">
						<h3 className="text-sm flex gap-2">
							<span className="py-0.5">{word.term}</span>
							{word.partOfSpeech &&
								(Array.isArray(word.partOfSpeech) ? (
									word.partOfSpeech.map((pos, idx) => (
										<motion.span
											key={idx}
											initial={{ opacity: 0, scale: 0.8 }}
											animate={{ opacity: 1, scale: 1 }}
											className="px-2 py-0.5 rounded-full bg-primary/10 text-muted-foreground"
										>
											{pos}
										</motion.span>
									))
								) : (
									<motion.span
										initial={{ opacity: 0, scale: 0.8 }}
										animate={{ opacity: 1, scale: 1 }}
										className="px-2 py-0.5 rounded-full bg-primary/10 text-muted-foreground"
									>
										{word.partOfSpeech}
									</motion.span>
								))}
						</h3>
					</div>
				</div>
			</div>
			<div className="flex items-center space-x-2">
				<div className="w-48">
					{addToCollectionLoading || localLoading ? (
						<div className="flex items-center justify-center h-9">
							<LoadingSpinner size="sm" />
						</div>
					) : added ? (
						<span className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium">
							{t('common.success')}
						</span>
					) : (
						<Button
							onClick={handleAddToCollection}
							className="w-full"
							disabled={!collections || collections.length === 0}
						>
							{t('words.add_to_collection')}
						</Button>
					)}
				</div>
			</div>
		</div>
	);
}
