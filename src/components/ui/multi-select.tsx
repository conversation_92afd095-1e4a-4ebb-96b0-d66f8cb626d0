'use client';

import { cn } from '@/lib';
import { AnimatePresence, motion } from 'framer-motion';
import { Check, ChevronDown, Plus } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Badge } from './badge';
import { Button } from './button';
import { Input } from './input';

export interface Option {
	value: string;
	label: string;
	action?: React.ReactNode;
}

export interface MultiSelectProps {
	options: Option[];
	selectedValues: string[];
	onChange: (values: string[]) => void;
	placeholder?: string;
	className?: string;
	allowNewOptions?: boolean;
}

export function MultiSelect({
	options,
	selectedValues,
	onChange,
	placeholder = 'Select options...',
	className,
	allowNewOptions = false,
}: MultiSelectProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [newOptionText, setNewOptionText] = useState('');
	const containerRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLInputElement>(null);

	// Handle clicking outside to close dropdown
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
				setIsOpen(false);
				setNewOptionText('');
			}
		};
		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	const handleOptionClick = useCallback(
		(value: string) => {
			onChange(
				selectedValues.includes(value)
					? selectedValues.filter((v) => v !== value)
					: [...selectedValues, value]
			);
		},
		[onChange, selectedValues]
	);

	const addNewOption = useCallback(() => {
		if (!newOptionText.trim()) return;
		const newValue = `temp_${Date.now()}_${newOptionText.trim()}`;
		onChange([...selectedValues, newValue]);
		setNewOptionText('');
		if (inputRef.current) {
			inputRef.current.focus();
		}
	}, [newOptionText, onChange, selectedValues]);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLInputElement>) => {
			if (e.key === 'Enter') {
				e.preventDefault();
				addNewOption();
			}
		},
		[addNewOption]
	);

	const tempSelectedValues = useMemo(
		() => selectedValues.filter((value) => !options.some((option) => option.value === value)),
		[selectedValues, options]
	);

	const tempOptions = useMemo(
		() =>
			tempSelectedValues.map((value) => ({
				value,
				label: value.split('_').slice(2).join('_'),
			})),
		[tempSelectedValues]
	);

	const allOptions = useMemo(
		() => [...options, ...tempOptions] as Option[],
		[options, tempOptions]
	);

	const selectedOptions = useMemo(
		() => allOptions.filter((option) => selectedValues.includes(option.value)),
		[allOptions, selectedValues]
	);

	const containerVariants = useMemo(
		() => ({
			hidden: { opacity: 0, y: -10 },
			visible: { opacity: 1, y: 0, transition: { duration: 0.2 } },
		}),
		[]
	);

	const dropdownVariants = useMemo(
		() => ({
			hidden: { opacity: 0, y: -10 },
			visible: { opacity: 1, y: 0, transition: { duration: 0.2, staggerChildren: 0.05 } },
		}),
		[]
	);

	const itemVariants = useMemo(
		() => ({
			hidden: { opacity: 0, x: -10 },
			visible: { opacity: 1, x: 0, transition: { duration: 0.2 } },
		}),
		[]
	);

	const badgeVariants = useMemo(
		() => ({
			hidden: { scale: 0.8, opacity: 0 },
			visible: { scale: 1, opacity: 1, transition: { duration: 0.2 } },
		}),
		[]
	);

	return (
		<motion.div
			className={cn('relative', className)}
			ref={containerRef}
			variants={containerVariants}
			initial="hidden"
			animate="visible"
		>
			<motion.div
				className={cn(
					'flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background/50 backdrop-blur-sm px-3 py-2 text-sm ring-offset-background cursor-pointer transition-all duration-200',
					isOpen && 'border-primary shadow-lg'
				)}
				onClick={() => setIsOpen((v) => !v)}
				whileHover={{ scale: 1.01 }}
				whileTap={{ scale: 0.99 }}
				role="button"
				aria-haspopup="listbox"
				aria-expanded={isOpen}
			>
				<div className="flex flex-wrap gap-1">
					{selectedOptions.length > 0 ? (
						<AnimatePresence mode="popLayout">
							{selectedOptions.map((option) => (
								<motion.div
									key={option.value}
									variants={badgeVariants}
									initial="hidden"
									animate="visible"
									exit="hidden"
								>
									<Badge
										variant="secondary"
										className="flex items-center gap-1 bg-primary/10 hover:bg-primary/20 transition-colors duration-200"
									>
										{option.label}
									</Badge>
								</motion.div>
							))}
						</AnimatePresence>
					) : (
						<span className="text-muted-foreground">{placeholder}</span>
					)}
				</div>
				<motion.div animate={{ rotate: isOpen ? 180 : 0 }} transition={{ duration: 0.2 }}>
					<ChevronDown className="h-4 w-4 opacity-50" />
				</motion.div>
			</motion.div>

			<AnimatePresence>
				{isOpen && (
					<motion.div
						variants={dropdownVariants}
						initial="hidden"
						animate="visible"
						exit="hidden"
						className={cn(
							'absolute max-h-60 w-full overflow-auto rounded-md border bg-background/80 backdrop-blur-sm p-1 text-popover-foreground shadow-lg space-y-1 z-10',
							selectedValues.length > 0 ? 'mt-1' : ''
						)}
						role="listbox"
					>
						{allOptions.map((option) => (
							<motion.div
								key={option.value}
								variants={itemVariants}
								className={cn(
									'relative flex cursor-pointer select-none items-center rounded-sm pl-8 pr-2 text-sm outline-none hover:bg-accent/50 hover:text-accent-foreground transition-colors duration-200',
									selectedValues.includes(option.value) && 'bg-accent/30'
								)}
								onClick={() => handleOptionClick(option.value)}
								title={undefined}
								role="option"
								aria-selected={selectedValues.includes(option.value)}
							>
								<motion.span
									className={cn(
										'absolute left-2 flex h-3.5 w-3.5 items-center justify-center',
										selectedValues.includes(option.value)
											? 'opacity-100'
											: 'opacity-0'
									)}
									animate={{
										scale: selectedValues.includes(option.value) ? 1 : 0.8,
									}}
									transition={{ duration: 0.2 }}
								>
									<Check className="h-4 w-4" />
								</motion.span>
								<span className="flex-1 truncate">{option.label}</span>
								{option.action && (
									<div onClick={(e) => e.stopPropagation()}>{option.action}</div>
								)}
							</motion.div>
						))}

						{allowNewOptions && (
							<motion.div
								variants={itemVariants}
								className={cn('pt-1', selectedValues.length > 0 ? 'mt-1' : '')}
							>
								<form
									onSubmit={(e) => {
										e.preventDefault();
										e.stopPropagation();
										addNewOption();
									}}
									className="flex items-center gap-2 px-2 py-1"
								>
									<Input
										ref={inputRef}
										type="text"
										placeholder="Add new keyword..."
										value={newOptionText}
										onChange={(e) => setNewOptionText(e.target.value)}
										onClick={(e) => e.stopPropagation()}
										className="flex-1 text-sm h-8 bg-background/50 backdrop-blur-sm focus:ring-2 focus:ring-primary/20 transition-all duration-200 px-2"
										wrapperClassName="w-full"
										style={{ minWidth: 0 }}
										onKeyDown={handleKeyDown}
									/>
									<motion.div
										whileHover={{ scale: 1.1 }}
										whileTap={{ scale: 0.9 }}
									>
										<Button
											type="submit"
											variant="ghost"
											size="icon"
											disabled={!newOptionText.trim()}
											className="h-8 w-8 flex items-center justify-center hover:bg-primary/10 hover:text-primary transition-colors duration-200"
											onClick={(e) => e.stopPropagation()}
										>
											<Plus className="h-4 w-4" />
										</Button>
									</motion.div>
								</form>
							</motion.div>
						)}
					</motion.div>
				)}
			</AnimatePresence>
		</motion.div>
	);
}
