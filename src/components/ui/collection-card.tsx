'use client';

import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui';
import { cn } from '@/lib/utils';
import { Collection } from '@prisma/client';
import { Edit, Eye, Trash } from 'lucide-react';

interface CollectionCardProps {
	collection: Collection;
	onViewDetails?: (collection: Collection) => void;
	onEdit?: (collection: Collection) => void;
	onDelete?: (collection: Collection) => void;
	className?: string;
}

export function CollectionCard({
	collection,
	onViewDetails,
	onEdit,
	onDelete,
	className,
}: CollectionCardProps) {
	return (
		<Card className={cn('w-full', className)}>
			<CardHeader>
				<CardTitle className="flex items-center justify-between">
					<span>{collection.name}</span>
					{/* <Badge variant="outline">{collection.word_ids.length} từ</Badge> */}
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="flex gap-2">
					<Button onClick={() => onViewDetails?.(collection)} className="flex-1">
						<Eye size={18} /> Xem chi tiết
					</Button>
					<Button variant="outline" onClick={() => onEdit?.(collection)}>
						<Edit size={18} />
					</Button>
					<Button variant="destructive" onClick={() => onDelete?.(collection)}>
						<Trash size={18} />
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
