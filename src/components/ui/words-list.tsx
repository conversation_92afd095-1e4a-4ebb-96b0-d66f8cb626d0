import { Card, LoadingSpinner, Translate } from '@/components/ui';
import { WordDetail } from '@/models';
import { Collection } from '@prisma/client';
import { motion } from 'framer-motion';
import { BookOpen } from 'lucide-react';
import { WordCard } from './word-card';

interface WordsListProps {
	loading: boolean;
	words: WordDetail[];
	detailedWords: Record<string, WordDetail>;
	detailLoading: Record<string, boolean>;
	expandedWords: Record<string, boolean>;
	collection: Collection | null;
	onToggleExpand: (term: string) => void;
	onGetDetails: (word: WordDetail) => void;
	onAddToCollection: (word: WordDetail) => Promise<void>;
	addToCollectionLoading: Record<string, boolean>;
}

export function WordsList({
	loading,
	words,
	onAddToCollection,
	addToCollectionLoading,
}: WordsListProps) {
	if (loading) {
		return (
			<div className="flex justify-center items-center py-12">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	if (words.length === 0) {
		return (
			<p className="text-center py-8 text-muted-foreground">
				<Translate text="words.no_words_found" />
			</p>
		);
	}

	return (
		<Card className="p-6">
			<div className="flex items-center space-x-4">
				<BookOpen className="h-5 w-5 text-primary" />
				<div>
					<h3 className="text-lg font-medium">Words List</h3>
				</div>
			</div>

			<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
				{words.map((word) => (
					<WordCard
						key={word.term}
						word={word}
						onAddToCollection={() => onAddToCollection(word)}
						isAddingToCollection={addToCollectionLoading[word.term]}
					/>
				))}
			</motion.div>
		</Card>
	);
}
