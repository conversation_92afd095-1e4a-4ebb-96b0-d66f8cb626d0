'use client';

import { CollectionWithDetail, WordDetail } from '@/models';
import {
	createContext,
	Dispatch,
	ReactNode,
	SetStateAction,
	useMemo,
	useState,
	useEffect,
	useCallback,
} from 'react';
import { useScopedLoading } from './loading-context';
import {
	getAllCollections,
	saveCollections,
	saveCollection,
	deleteCollection as deleteCollectionFromDB,
} from '@/lib/indexed-db/collection-indexed-db';

type LoadingState = {
	fetch: boolean; // Loading collections list
	get: boolean; // Loading collection details
	getWordsToReview: boolean; // Loading words to review list
	search: boolean; // Searching collections
	create: boolean; // Creating new collection
	update: boolean; // Updating collection
	delete: boolean; // Deleting collection
	addTerm: boolean; // Adding term to collection
	addWords: boolean; // Adding words to collection
	removeWords: boolean; // Removing words from collection
	setCurrent: boolean; // Setting current collection

	// Words loading states
	wordsSearch: boolean; // Searching words
	fetchWords: boolean; // Fetching words by collection
	getWordsToReviewWords: boolean; // Getting words to review
	bulkDeleteWords: boolean; // Bulk deleting words
	fetchWord: boolean; // Fetching individual word
};

// Context type definition
export type CollectionsContextType = {
	// Collections state
	collections: CollectionWithDetail[];
	setCollections: Dispatch<SetStateAction<CollectionWithDetail[]>>;
	currentCollection: CollectionWithDetail | null;
	setCurrentCollection: Dispatch<SetStateAction<CollectionWithDetail | null>>;
	loading: LoadingState;
	error: Error | null;
	setError: Dispatch<SetStateAction<Error | null>>;
	currentCollectionWords: WordDetail[]; // Words specifically for current collection
	setCurrentCollectionWords: Dispatch<SetStateAction<WordDetail[]>>;
};

const CollectionsContext = createContext<CollectionsContextType | undefined>(undefined);

export function CollectionsProvider({ children }: { children: ReactNode }) {
	const [collections, setCollections] = useState<CollectionWithDetail[]>([]);
	const [currentCollection, setCurrentCollection] = useState<CollectionWithDetail | null>(null);
	const [error, setError] = useState<Error | null>(null);
	const [isInitialized, setIsInitialized] = useState(false);

	// Words state - unified for both general and current collection
	const [currentCollectionWords, setCurrentCollectionWords] = useState<WordDetail[]>([]);
	const { getLoading } = useScopedLoading('collections');

	const loadCollectionsFromDB = useCallback(async () => {
		try {
			const savedCollections = await getAllCollections();
			setCollections(savedCollections);
		} catch (err) {
			console.error('Failed to load collections from IndexedDB:', err);
			setError(err as Error);
		}
	}, []);

	useEffect(() => {
		if (!isInitialized) {
			loadCollectionsFromDB();
			setIsInitialized(true);
		}
	}, [isInitialized]);

	useEffect(() => {
		saveCollections(collections);
	}, [collections]);

	useEffect(() => {
		if (currentCollection) {
		} else {
			setCurrentCollectionWords([]);
		}
	}, [currentCollection]);

	// Create a loading state object for backward compatibility
	const loading: LoadingState = useMemo(
		() => ({
			fetch: getLoading('fetch'),
			get: getLoading('get'),
			getWordsToReview: getLoading('getWordsToReview'),
			search: getLoading('search'),
			create: getLoading('create'),
			update: getLoading('update'),
			delete: getLoading('delete'),
			addTerm: getLoading('addTerm'),
			addWords: getLoading('addWords'),
			removeWords: getLoading('removeWords'),
			setCurrent: getLoading('setCurrent'),
			wordsSearch: getLoading('wordsSearch'),
			fetchWords: getLoading('fetchWords'),
			getWordsToReviewWords: getLoading('getWordsToReviewWords'),
			bulkDeleteWords: getLoading('bulkDeleteWords'),
			fetchWord: getLoading('fetchWord'),
		}),
		[getLoading]
	);

	const contextValue = useMemo<CollectionsContextType>(
		() => ({
			collections,
			setCollections,
			currentCollection,
			setCurrentCollection,

			loading,
			error,
			setError,

			currentCollectionWords,
			setCurrentCollectionWords,
		}),
		[
			collections,
			setCollections,
			currentCollection,
			setCurrentCollection,

			loading,
			error,
			setError,

			currentCollectionWords,
			setCurrentCollectionWords,
		]
	);

	return (
		<CollectionsContext.Provider value={contextValue}>{children}</CollectionsContext.Provider>
	);
}

export { CollectionsContext };
